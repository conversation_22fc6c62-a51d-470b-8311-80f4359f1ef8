<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>样式保存测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-content {
            background: #f9f9f9;
            padding: 10px;
            margin: 10px 0;
            border-left: 4px solid #007cba;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a8b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TinyMCE 样式保存测试</h1>
        
        <div class="test-section">
            <h3>测试步骤：</h3>
            <ol>
                <li>点击"创建测试文章"按钮创建一个包含样式的测试文章</li>
                <li>点击"打开编辑页面"在新窗口中打开编辑器</li>
                <li>在编辑器中添加一些格式化内容（颜色、字体大小、样式等）</li>
                <li>保存文章</li>
                <li>回到此页面，点击"检查样式保存"验证样式是否被保留</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>操作按钮：</h3>
            <button onclick="createTestArticle()">创建测试文章</button>
            <button onclick="openEditPage()">打开编辑页面</button>
            <button onclick="checkStylePreservation()">检查样式保存</button>
        </div>

        <div class="test-section">
            <h3>测试结果：</h3>
            <div id="result-area">
                <p>等待测试...</p>
            </div>
        </div>

        <div class="test-section">
            <h3>原始HTML内容：</h3>
            <div id="html-content" class="test-content">
                <p>等待加载...</p>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        const TEST_TITLE = "样式保存测试文章_" + Date.now();
        
        function createTestArticle() {
            const testHtml = `
                <h2 style="color: #ff6600; text-align: center;">测试标题</h2>
                <p style="color: #0066cc; font-size: 18px; font-weight: bold;">这是一个蓝色粗体段落</p>
                <p style="background-color: #ffff99; padding: 10px; border: 2px solid #ff0000;">这是一个黄色背景红色边框的段落</p>
                <p style="font-family: 'Times New Roman', serif; font-style: italic; text-decoration: underline;">这是一个斜体下划线段落</p>
                <div style="text-align: right; color: #666666;">右对齐的灰色文字</div>
            `;
            
            $.ajax({
                type: "POST",
                url: "/apis/update_wechat_article_by_title/",
                data: {
                    title: TEST_TITLE,
                    html_content: testHtml
                },
                success: function(data) {
                    if (data.status === 'ok') {
                        $('#result-area').html('<p class="success">✅ 测试文章创建成功！</p>');
                    } else {
                        $('#result-area').html('<p class="error">❌ 创建失败: ' + data.msg + '</p>');
                    }
                },
                error: function() {
                    $('#result-area').html('<p class="error">❌ 网络错误</p>');
                }
            });
        }
        
        function openEditPage() {
            const url = `h5/wechat_article_detail.html?title=${encodeURIComponent(TEST_TITLE)}`;
            window.open(url, '_blank');
        }
        
        function checkStylePreservation() {
            $.ajax({
                type: "POST",
                url: "/apis/get_wechat_article_by_title/",
                data: {
                    title: TEST_TITLE
                },
                success: function(data) {
                    if (data.status === 'ok' && data.data && data.data.length > 0) {
                        const htmlContent = data.data[0].html_content;
                        $('#html-content').html(htmlContent);
                        
                        // 检查样式是否保留
                        let styleCount = 0;
                        const styleChecks = [
                            'color:',
                            'font-size:',
                            'font-weight:',
                            'background-color:',
                            'border:',
                            'text-align:',
                            'font-family:',
                            'font-style:',
                            'text-decoration:'
                        ];
                        
                        styleChecks.forEach(style => {
                            if (htmlContent.includes(style)) {
                                styleCount++;
                            }
                        });
                        
                        if (styleCount >= 5) {
                            $('#result-area').html(`
                                <p class="success">✅ 样式保存测试通过！</p>
                                <p>检测到 ${styleCount} 种样式属性被正确保存</p>
                            `);
                        } else {
                            $('#result-area').html(`
                                <p class="error">❌ 样式保存测试失败！</p>
                                <p>只检测到 ${styleCount} 种样式属性，可能样式丢失</p>
                            `);
                        }
                    } else {
                        $('#result-area').html('<p class="error">❌ 获取文章失败: ' + (data.msg || '未知错误') + '</p>');
                    }
                },
                error: function() {
                    $('#result-area').html('<p class="error">❌ 网络错误</p>');
                }
            });
        }
    </script>
</body>
</html>
