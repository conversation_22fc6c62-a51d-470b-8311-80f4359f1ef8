/**
 * 服务与支持页面的功能和事件处理
 */
// 在文件最开始添加
console.log('=== support.js 开始加载 ===');

// 调试工具对象
const DebugTools = {
    logMenuState: function() {
        console.group('=== 菜单状态详细信息 ===');
        try {
            const state = localStorage.getItem('supportMenuState');
            console.log('当前时间:', new Date().toLocaleString());
            console.log('当前页面:', window.location.pathname);
            console.log('原始状态数据:', state);
            
            if (state) {
                const parsedState = JSON.parse(state);
                console.log('解析后的状态:', {
                    menuId: parsedState.menuId,
                    submenuId: parsedState.submenuId,
                    parentMenu: parsedState.parentMenu,
                    lastPath: parsedState.lastPath,
                    保存时间: new Date(parsedState.timestamp).toLocaleString()
                });
            } else {
                console.log('localStorage中没有保存的菜单状态');
            }
        } catch (e) {
            console.error('读取状态出错:', e);
        }
        console.groupEnd();
    }
};

// 在document.ready之前先输出一次状态
console.log('=== 页面加载前的菜单状态 ===');
DebugTools.logMenuState();

// 兼容各浏览器的强制置顶函数
function forceScrollTop() {
    window.scrollTo(0, 0);
    if (document.documentElement) document.documentElement.scrollTop = 0;
    if (document.body) document.body.scrollTop = 0;
    var wrapper = document.getElementById('wrapper');
    if (wrapper) wrapper.scrollTop = 0;
}

$(document).ready(function() {
    setTimeout(forceScrollTop, 50); // 页面加载时强制置顶，兼容国产浏览器
    console.log('=== document.ready 触发 ===');
    DebugTools.logMenuState();

    // 语言切换缓存清理机制
    clearLanguageSwitchCache();
    
    // 判断当前页面是否是support.html或support_en.html
    function isSupportPage() {
        const path = window.location.pathname;
        return path.endsWith('/support.html') || path.endsWith('/support_en.html');
    }

    // 如果是支持页面，强制设置终端菜单为展开状态
    if (isSupportPage()) {
        const isEnglish = isEnglishSite();
        const terminalMenuTitle = isEnglish ? 'Terminal' : '终端';
        
        // 保存新的菜单状态，强制只展开终端菜单
        try {
            const newState = {
                menuId: null,
                parentMenu: terminalMenuTitle,
                isExpanded: true,
                timestamp: Date.now()
            };
            localStorage.setItem('supportMenuState', JSON.stringify(newState));
            console.log('初始化终端菜单状态:', newState);
        } catch (e) {
            console.error('保存菜单状态出错:', e);
        }
    }
    
    // 在文件开头添加全局菜单状态管理
    // 菜单状态管理
    const MenuStateManager = {
        // 获取状态
        getState: function() {
            try {
                return JSON.parse(localStorage.getItem('supportMenuState') || '{}');
            } catch (e) {
                console.error('读取菜单状态出错:', e);
                return {};
            }
        },
        
        // 保存状态
        setState: function(state) {
            try {
                localStorage.setItem('supportMenuState', JSON.stringify(state));
            } catch (e) {
                console.error('保存菜单状态出错:', e);
            }
        }
    };

    // 检查是否为英文站点
    function isEnglishSite() {
        return document.documentElement.lang === 'en' || 
               window.location.pathname.includes('_en.html');
    }

    // 语言切换缓存清理
    function clearLanguageSwitchCache() {
        const currentPath = window.location.pathname;
        const lastPath = localStorage.getItem('lastSupportPath');
        
        if (lastPath && lastPath !== currentPath) {
            // 检查是否是语言切换
            const isLanguageSwitch = (
                (lastPath.includes('support.html') && currentPath.includes('support_en.html')) ||
                (lastPath.includes('support_en.html') && currentPath.includes('support.html'))
            );
            
            if (isLanguageSwitch) {
                console.log('检测到语言切换，清理菜单状态');
                localStorage.removeItem('supportMenuState');
            }
        }
        
        localStorage.setItem('lastSupportPath', currentPath);
    }

    // 初始化页面
    initializePage();

    function initializePage() {
        console.log('=== 开始初始化页面 ===');
        
        // 显示页面
        $('#wrapper').show();
        
        // 加载菜单数据
        loadMenuData();

        // 绑定事件
        bindEvents();
        
        console.log('=== 页面初始化完成 ===');
    }

    // 加载菜单数据
    function loadMenuData() {
        const isEnglish = isEnglishSite();
        const lang = isEnglish ? 1 : 0;

        console.log('开始加载支持菜单数据，语言:', isEnglish ? '英文' : '中文');

        // 从后台API获取支持数据
        $.ajax({
            type: "get",
            url: "/apis/support_list/",
            data: {
                lang: lang,
                type: '菜单'  // 只获取菜单类型的数据
            },
            success: function (data) {
                if (data.status === 'ok' && data.data) {
                    console.log('获取到支持数据:', data.data);
                    processMenuData(data.data);
                } else {
                    console.log('没有获取到支持数据，使用默认菜单');
                    loadDefaultMenuData();
                }
            },
            error: function () {
                console.error('获取支持数据失败，使用默认菜单');
                loadDefaultMenuData();
            }
        });
    }

    // 处理从后台获取的菜单数据
    function processMenuData(supportData) {
        // 按display_order排序
        supportData.sort((a, b) => (a.display_order || 100) - (b.display_order || 100));

        // 将数据按标题分组（标题作为父菜单，内容作为子菜单）
        const menuGroups = {};

        supportData.forEach(item => {
            if (!item.show) return; // 跳过不显示的项目

            const title = item.title || '未分类';
            if (!menuGroups[title]) {
                menuGroups[title] = {
                    title: title,
                    items: []
                };
            }

            // 将content按行分割作为子菜单项
            if (item.content) {
                const contentLines = item.content.split('\n').filter(line => line.trim());
                contentLines.forEach((line, index) => {
                    menuGroups[title].items.push({
                        name: line.trim(),
                        id: `${item.id}-${index}`,
                        url: item.url || '#',
                        image_path: item.image_path
                    });
                });
            }
        });

        // 转换为数组格式
        const menuData = Object.values(menuGroups);
        console.log('处理后的菜单数据:', menuData);

        if (menuData.length > 0) {
            renderMenu(menuData);
        } else {
            loadDefaultMenuData();
        }
    }

    // 加载默认菜单数据（备用）
    function loadDefaultMenuData() {
        const isEnglish = isEnglishSite();

        const defaultMenuData = isEnglish ? [
            {
                title: 'Technical Support',
                items: [
                    { name: 'Online Documentation', id: 'docs', url: '#' },
                    { name: 'FAQ', id: 'faq', url: '#' },
                    { name: 'Video Tutorials', id: 'videos', url: '#' }
                ]
            },
            {
                title: 'Contact Us',
                items: [
                    { name: 'Technical Hotline', id: 'hotline', url: '#' },
                    { name: 'Email Support', id: 'email', url: '#' }
                ]
            }
        ] : [
            {
                title: '技术支持',
                items: [
                    { name: '在线文档', id: 'docs', url: '#' },
                    { name: '常见问题', id: 'faq', url: '#' },
                    { name: '视频教程', id: 'videos', url: '#' }
                ]
            },
            {
                title: '联系我们',
                items: [
                    { name: '技术热线', id: 'hotline', url: '#' },
                    { name: '邮件支持', id: 'email', url: '#' }
                ]
            }
        ];

        renderMenu(defaultMenuData);
    }

    // 渲染菜单（完全按照资料下载页面的设计）
    function renderMenu(menuData) {
        const sidebar = $('.support-sidebar');
        sidebar.empty();

        menuData.forEach((category, index) => {
            const groupId = `support-group-${index}`;
            const menuHtml = `
                <div class="expandable-menu" id="${groupId}" data-title="${category.title}">
                    <div class="menu-title">
                        ${category.title}
                        <img src="./images/home/<USER>" class="arrow-icon" alt="展开">
                    </div>
                    <div class="menu-divider" style="display: none;"></div>
                    <div class="menu-items" style="display: none;">
                        ${category.items.map(item => `
                            <div class="menu-item" data-id="${item.id}" data-url="${item.url || '#'}">
                                <a href="${item.url || '#'}" data-id="${item.id}">${item.name}</a>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            sidebar.append(menuHtml);
        });

        // 绑定菜单事件
        bindMenuEvents();

        // 使用setTimeout确保DOM完全渲染后再执行选中逻辑
        setTimeout(() => {
            // 恢复菜单状态或默认选中第一个菜单项，并加载对应内容
            const selectedTitle = restoreMenuStateOrDefault();

            console.log('=== 初始化完成后的状态检查 ===');
            console.log('选中的标题:', selectedTitle);
            console.log('菜单项总数:', $('.menu-item').length);
            console.log('展开的菜单数:', $('.expandable-menu.expanded').length);
            console.log('选中的菜单项数:', $('.menu-item.active').length);

            // 加载初始支持内容
            if (selectedTitle) {
                console.log('加载初始内容，标题:', selectedTitle);

                // 检测是否为英文界面，如果是则根据显示顺序获取对应的中文标题
                const isEnglish = document.documentElement.lang === 'en' ||
                                 window.location.pathname.includes('_en.html');

                console.log('初始化时的语言检测结果:', {
                    documentLang: document.documentElement.lang,
                    pathname: window.location.pathname,
                    isEnglish: isEnglish,
                    selectedTitle: selectedTitle
                });

                if (isEnglish) {
                    // 找到当前选中菜单项的索引
                    const $activeMenuItem = $('.menu-item.active');
                    if ($activeMenuItem.length > 0) {
                        const $parentMenu = $activeMenuItem.closest('.expandable-menu');
                        const $allMenuItems = $parentMenu.find('.menu-item');
                        const itemIndex = $allMenuItems.index($activeMenuItem);

                        const $allMenus = $('.expandable-menu');
                        const menuIndex = $allMenus.index($parentMenu);

                        console.log('初始化时的英文界面 - 菜单顺序:', {
                            menuIndex: menuIndex,
                            itemIndex: itemIndex,
                            englishText: selectedTitle
                        });

                        // 根据顺序获取对应的中文标题
                        loadSupportContentByOrder(menuIndex, itemIndex, selectedTitle);
                    } else {
                        console.log('未找到选中的菜单项，显示默认内容');
                        displayDefaultContent();
                    }
                } else {
                    loadSupportContentByTitle(selectedTitle);
                }
            } else {
                console.log('没有选中的菜单项，显示默认内容');
                displayDefaultContent();
            }
        }, 100); // 延迟100ms确保DOM渲染完成
    }

    // 绑定菜单事件（完全按照资料下载页面的逻辑）
    function bindMenuEvents() {
        // 绑定菜单标题点击事件
        $('.menu-title').off('click').on('click', function() {
            var $menu = $(this).closest('.expandable-menu');
            var wasExpanded = $menu.hasClass('expanded');
            var menuTitle = $menu.data('title');

            console.log('点击菜单:', menuTitle, '当前状态:', wasExpanded ? '展开' : '收起');

            if (wasExpanded) {
                // 收起当前菜单
                console.log('收起菜单:', menuTitle);
                $menu.removeClass('expanded');
                $menu.find('.arrow-icon').attr('src', './images/home/<USER>');
                $menu.find('.menu-items').hide();
                $menu.find('.menu-divider').hide();
            } else {
                // 展开当前菜单
                console.log('展开菜单:', menuTitle);
                $menu.addClass('expanded');
                $menu.find('.arrow-icon').attr('src', './images/home/<USER>');
                $menu.find('.menu-items').show();
                $menu.find('.menu-divider').show();
            }
        });

        // 绑定菜单项点击事件
        $('.menu-item a').off('click').on('click', function(e) {
            e.preventDefault();

            const menuItem = $(this).closest('.menu-item');
            const itemId = menuItem.data('id');
            const itemUrl = menuItem.data('url');

            // 更新选中状态
            $('.menu-item').removeClass('active');
            menuItem.addClass('active');

            // 如果有URL且不是#，则跳转
            if (itemUrl && itemUrl !== '#') {
                window.open(itemUrl, '_blank');
            }

            // 根据菜单项标题加载对应的支持内容（从编辑器保存的数据）
            const itemText = $(this).text().trim();
            console.log('点击菜单项:', {
                id: itemId,
                url: itemUrl,
                text: itemText
            });

            // 检测是否为英文界面，如果是则根据显示顺序获取对应的中文标题
            const isEnglish = document.documentElement.lang === 'en' ||
                             window.location.pathname.includes('_en.html');

            console.log('语言检测结果:', {
                documentLang: document.documentElement.lang,
                pathname: window.location.pathname,
                isEnglish: isEnglish
            });

            if (isEnglish) {
                // 获取当前菜单项在所属分组中的顺序
                const $parentMenu = menuItem.closest('.expandable-menu');
                const $allMenuItems = $parentMenu.find('.menu-item');
                const itemIndex = $allMenuItems.index(menuItem);

                // 获取父分组在所有分组中的顺序
                const $allMenus = $('.expandable-menu');
                const menuIndex = $allMenus.index($parentMenu);

                console.log('英文界面 - 菜单顺序:', {
                    menuIndex: menuIndex,
                    itemIndex: itemIndex,
                    englishText: itemText
                });

                // 根据顺序获取对应的中文标题
                loadSupportContentByOrder(menuIndex, itemIndex, itemText);
            } else {
                loadSupportContentByTitle(itemText);
            }

            // 保存菜单状态
            const $menu = menuItem.closest('.expandable-menu');
            const menuId = $menu.attr('id');
            try {
                localStorage.setItem('supportMenuState', JSON.stringify({
                    menuId: menuId,
                    submenuId: itemId,
                    parentMenu: $menu.data('title'),
                    selectedTitle: itemText,  // 保存选中的标题
                    timestamp: Date.now()
                }));
            } catch (e) {
                console.error('保存菜单状态失败:', e);
            }
        });
    }

    // 支持页面专用的菜单初始化逻辑（不依赖资料下载页面的逻辑）
    function restoreMenuStateOrDefault() {
        console.log('=== 支持页面菜单初始化开始 ===');

        // 清除旧的localStorage状态（避免与资料下载页面冲突）
        localStorage.removeItem('supportMenuState');
        localStorage.removeItem('menuState'); // 清除资料下载页面的状态

        console.log('已清除旧的菜单状态');

        // 强制展开第一个菜单项（支持页面专用逻辑）
        console.log('开始强制展开第一个菜单项...');

        // 获取所有菜单项
        const $allMenus = $('.expandable-menu');
        console.log('找到的菜单总数:', $allMenus.length);

        if ($allMenus.length > 0) {
            const $firstMenu = $allMenus.first();
            const menuTitle = $firstMenu.find('.menu-title').text().trim();
            console.log('第一个菜单项标题:', menuTitle);

            // 强制展开第一个菜单项
            console.log('正在展开第一个菜单项...');
            $firstMenu.addClass('expanded');

            // 更新箭头图标
            const $arrow = $firstMenu.find('.arrow-icon');
            if ($arrow.length) {
                $arrow.attr('src', './images/home/<USER>');
                console.log('箭头图标已更新');
            }

            // 显示菜单项和分隔线
            const $menuItems = $firstMenu.find('.menu-items');
            const $divider = $firstMenu.find('.menu-divider');

            $menuItems.show();
            $divider.show();

            console.log('菜单项和分隔线已显示');
            console.log('菜单项容器:', $menuItems.length);
            console.log('分隔线:', $divider.length);

            // 查找第一个子菜单项
            const $subMenuItems = $firstMenu.find('.menu-item');
            console.log('找到的子菜单项数量:', $subMenuItems.length);

            if ($subMenuItems.length > 0) {
                const $firstSubMenu = $subMenuItems.first();
                const subMenuTitle = $firstSubMenu.find('a').text().trim();
                console.log('第一个子菜单项标题:', subMenuTitle);

                // 选中第一个子菜单项
                $('.menu-item').removeClass('active');
                $firstSubMenu.addClass('active');

                console.log('第一个子菜单项已选中');
                console.log('=== 支持页面菜单初始化完成 ===');

                return subMenuTitle;
            } else {
                console.log('第一个菜单项中没有找到子菜单项');
                return null;
            }
        } else {
            console.log('没有找到任何菜单项');
            return null;
        }
    }



    // 显示默认内容
    function displayDefaultContent() {
        const defaultHtml = `
            <div class="default-content">
                <div class="welcome-section">
                    <h2>欢迎使用技术支持</h2>
                    <p>请从左侧菜单选择您需要的技术支持内容</p>
                </div>
                <div class="features-section">
                    <h3>我们提供的服务</h3>
                    <ul>
                        <li>详细的技术文档和说明</li>
                        <li>产品使用指南和教程</li>
                        <li>常见问题解答</li>
                        <li>技术支持和咨询</li>
                    </ul>
                </div>
                <div class="contact-section">
                    <h3>联系我们</h3>
                    <p>如果您有任何问题，请随时联系我们的技术支持团队</p>
                </div>
            </div>
        `;

        $('#left-column').html(defaultHtml);
        addContentStyles(); // 只为默认内容添加基础样式
    }



    // 根据显示顺序加载对应的中文支持内容（英文界面专用）
    function loadSupportContentByOrder(menuIndex, itemIndex, englishTitle) {
        console.log('=== 根据显示顺序加载中文支持内容 ===');
        console.log('菜单索引:', menuIndex, '项目索引:', itemIndex, '英文标题:', englishTitle);

        // 显示加载状态
        $('#left-column').html('<div class="loading-content"></div>');

        // 调用API获取中文菜单数据，然后根据顺序找到对应的中文标题
        $.ajax({
            type: "GET",
            url: "/apis/support_list/",
            data: {
                lang: 0,  // 强制获取中文菜单数据
                type: '菜单'  // 只获取菜单类型的数据
            },
            success: function (response) {
                console.log('中文菜单API响应:', response);

                if (response.status === 'ok' && response.data && response.data.length > 0) {
                    // 处理菜单数据，与processMenuData函数逻辑一致
                    const supportData = response.data;
                    supportData.sort((a, b) => (a.display_order || 100) - (b.display_order || 100));

                    // 将数据按标题分组
                    const menuGroups = {};
                    supportData.forEach(item => {
                        if (!item.show) return;

                        const title = item.title || '未分类';
                        if (!menuGroups[title]) {
                            menuGroups[title] = {
                                title: title,
                                items: []
                            };
                        }

                        if (item.content) {
                            const contentLines = item.content.split('\n').filter(line => line.trim());
                            contentLines.forEach((line, index) => {
                                menuGroups[title].items.push({
                                    name: line.trim(),
                                    id: `${item.id}-${index}`,
                                    url: item.url || '#',
                                    image_path: item.image_path
                                });
                            });
                        }
                    });

                    // 转换为数组格式
                    const menuData = Object.values(menuGroups);
                    console.log('处理后的中文菜单数据:', menuData);

                    // 根据顺序找到对应的中文标题
                    if (menuData[menuIndex] && menuData[menuIndex].items && menuData[menuIndex].items[itemIndex]) {
                        const chineseTitle = menuData[menuIndex].items[itemIndex].name;
                        console.log('找到对应的中文标题:', chineseTitle);

                        // 使用中文标题加载内容
                        loadSupportContentByTitle(chineseTitle);
                    } else {
                        console.log('未找到对应顺序的中文菜单项，菜单索引:', menuIndex, '项目索引:', itemIndex);
                        console.log('可用的菜单数据:', menuData);
                        displayNoContent(englishTitle);
                    }
                } else {
                    console.log('获取中文菜单数据失败');
                    displayErrorContent(englishTitle, '无法获取中文菜单数据');
                }
            },
            error: function(xhr, status, error) {
                console.error('获取中文菜单数据出错:', error);
                displayErrorContent(englishTitle, error);
            }
        });
    }

    // 根据标题加载支持内容（从编辑器保存的数据）- 仅用于中文界面
    function loadSupportContentByTitle(title) {
        console.log('=== 根据标题加载支持内容 ===');
        console.log('搜索标题:', title);

        // 显示加载状态
        $('#left-column').html('<div class="loading-content"></div>');

        // 调用API获取支持内容详情数据
        $.ajax({
            type: "POST",
            url: "/apis/get_support_by_title/",
            data: {
                title: title,  // 使用传入的标题搜索
                type: '详情'  // 只获取详情类型的数据
            },
            success: function (response) {
                console.log('API响应:', response);

                if (response.status === 'ok' && response.data && response.data.length > 0) {
                    // 只使用第一个匹配的数据（参考编辑器的逻辑）
                    const supportData = response.data[0];
                    console.log('找到匹配的支持内容:', supportData.title);

                    // 优先使用html_content，如果没有则使用content
                    let contentToDisplay = '';
                    if (supportData.html_content && supportData.html_content.trim()) {
                        contentToDisplay = supportData.html_content;
                        console.log('使用HTML内容，长度:', contentToDisplay.length);
                    } else if (supportData.content && supportData.content.trim()) {
                        // 如果没有HTML内容，构建包含标题的HTML
                        contentToDisplay = `<h2>${supportData.title}</h2>${supportData.content}`;
                        console.log('构建HTML内容，长度:', contentToDisplay.length);
                    } else {
                        contentToDisplay = `<h2>${supportData.title}</h2><p>暂无详细内容</p>`;
                    }

                    // 将HTML内容加载到左侧区域
                    displaySupportContent(contentToDisplay, supportData);

                } else {
                    console.log('未找到匹配的支持内容');
                    displayNoContent(title);
                }
            },
            error: function(xhr, status, error) {
                console.error('加载支持内容失败:', error);
                console.error('状态:', status);
                console.error('响应:', xhr.responseText);
                displayErrorContent(title, error);
            }
        });
    }

    // 显示支持内容（支持预翻译缓存机制）
    function displaySupportContent(htmlContent, supportData) {
        console.log('显示支持内容:', supportData.title);

        // 检测是否为英文界面
        const isEnglish = document.documentElement.lang === 'en' ||
                         window.location.pathname.includes('_en.html');

        if (isEnglish) {
            console.log('检测到英文界面，检查翻译缓存');

            // 尝试从缓存获取翻译内容
            const cacheKey = generateCacheKey(supportData.title, htmlContent);
            const cachedTranslation = getTranslationCache(cacheKey);

            if (cachedTranslation) {
                console.log('使用缓存的翻译内容:', supportData.title);
                $('#left-column').html(cachedTranslation);
            } else {
                console.log('缓存中没有翻译内容，开始实时翻译');
                // 翻译HTML内容并缓存
                translateContent(htmlContent).then(translatedContent => {
                    // 保存翻译结果到缓存
                    setTranslationCache(cacheKey, translatedContent);
                    $('#left-column').html(translatedContent);
                    console.log('翻译完成并已缓存:', supportData.title);
                }).catch(error => {
                    console.error('翻译失败，使用原始内容:', error);
                    $('#left-column').html(htmlContent);
                });
            }
        } else {
            // 中文界面：显示原始内容并预翻译缓存
            $('#left-column').html(htmlContent);
            console.log('中文界面内容已加载，开始预翻译缓存');

            // 在后台预翻译并缓存（不影响当前显示）
            preTranslateAndCache(supportData.title, htmlContent);
        }
    }

    // 显示无内容状态
    function displayNoContent(title) {
        const noContentHtml = `
            <div class="no-content">
                <div class="no-content-icon">
                    <i class="fa fa-file-text-o"></i>
                </div>
                <h3>未找到相关内容</h3>
                <p>没有找到标题为 "${title}" 的支持内容</p>
                <p>请检查菜单项名称是否与编辑器中保存的标题完全匹配</p>
            </div>
        `;
        $('#left-column').html(noContentHtml);
    }

    // 显示错误状态
    function displayErrorContent(title, error) {
        const errorHtml = `
            <div class="error-content">
                <div class="error-icon">
                    <i class="fa fa-exclamation-triangle"></i>
                </div>
                <h3>加载失败</h3>
                <p>加载标题为 "${title}" 的内容时出现错误</p>
                <p class="error-detail">错误信息: ${error}</p>
                <button class="btn btn-primary" onclick="loadSupportContentByTitle('${title}')">重试</button>
            </div>
        `;
        $('#left-column').html(errorHtml);
    }

    // 添加基础样式（只保留必要的样式）
    function addContentStyles() {
        // 如果样式还没有添加，则添加
        if (!$('#support-content-styles').length) {
            const styles = `
                <style id="support-content-styles">
                .loading-content, .no-content, .error-content {
                    text-align: center;
                    padding: 40px 20px;
                    color: #666;
                }
                .no-content-icon, .error-icon {
                    font-size: 48px;
                    margin-bottom: 20px;
                    color: #ccc;
                }
                .error-icon {
                    color: #dc3545;
                }
                .error-detail {
                    font-size: 12px;
                    color: #999;
                    margin-bottom: 20px;
                }
                .default-content {
                    padding: 20px;
                }
                </style>
            `;
            $('head').append(styles);
        }
    }

    // 格式化日期时间
    function formatDateTime(dateString) {
        if (!dateString) return '未知';

        try {
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}`;
        } catch (e) {
            return '格式错误';
        }
    }

    // 绑定其他事件
    function bindEvents() {
        // 支持操作点击
        $(document).on('click', '.support-action', function() {
            const actionType = $(this).find('span').text();
            const itemName = $(this).siblings('.item-info').find('.item-name').text();

            console.log(`执行操作: ${actionType} - ${itemName}`);

            // 这里可以添加具体的操作逻辑
            alert(`${actionType}: ${itemName}`);
        });
    }

    // 加载支持内容
    function loadSupportContent(itemId) {
        console.log('加载支持内容:', itemId);

        // 清空现有内容，只留下容器
        $('#left-column, #right-column').empty();

        // 这里可以根据itemId加载具体的支持内容
        // 目前只清空内容，保持容器结构
        console.log('支持内容已清空，等待具体内容加载');
    }

    // 页脚加载逻辑（复制自download.js）
    function loadFooter() {
        console.log('开始加载页脚...');

        // 动态创建footer标签并插入body末尾
        if ($('footer').length === 0) {
            $('body').append('<footer></footer>');
            console.log('已创建footer标签');
        }

        // 动态加载footer.js并初始化footer
        var showFooter = function() {
            if (typeof window.initFooter === 'function') {
                console.log('初始化页脚...');
                window.initFooter();
                console.log('页脚初始化完成');
            } else {
                console.log('initFooter函数不存在');
            }
        };

        if (!window.initFooter) {
            console.log('加载footer.js...');
            var script = document.createElement('script');
            script.src = 'js/footer.js';
            script.onload = function() {
                console.log('footer.js加载完成');
                showFooter();
            };
            script.onerror = function() {
                console.error('footer.js加载失败');
            };
            document.body.appendChild(script);
        } else {
            showFooter();
        }
    }

    // 翻译HTML内容的函数
    async function translateContent(htmlContent) {
        console.log('开始翻译内容...');

        try {
            // 创建临时DOM元素来解析HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;

            // 获取所有文本节点和需要翻译的元素
            const elementsToTranslate = [];

            // 递归遍历所有节点，收集需要翻译的文本
            function collectTextNodes(node) {
                if (node.nodeType === Node.TEXT_NODE) {
                    const text = node.textContent.trim();
                    if (text && containsChinese(text)) {
                        elementsToTranslate.push({
                            node: node,
                            originalText: text
                        });
                    }
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                    // 检查元素的属性（如alt, title等）
                    if (node.alt && containsChinese(node.alt)) {
                        elementsToTranslate.push({
                            node: node,
                            originalText: node.alt,
                            attribute: 'alt'
                        });
                    }
                    if (node.title && containsChinese(node.title)) {
                        elementsToTranslate.push({
                            node: node,
                            originalText: node.title,
                            attribute: 'title'
                        });
                    }

                    // 递归处理子节点
                    for (let child of node.childNodes) {
                        collectTextNodes(child);
                    }
                }
            }

            collectTextNodes(tempDiv);
            console.log('找到需要翻译的文本数量:', elementsToTranslate.length);

            // 批量翻译所有文本
            for (let item of elementsToTranslate) {
                try {
                    const translatedText = await translateText(item.originalText);
                    if (item.attribute) {
                        item.node[item.attribute] = translatedText;
                    } else {
                        item.node.textContent = translatedText;
                    }
                } catch (error) {
                    console.warn('翻译单个文本失败:', item.originalText, error);
                }
            }

            console.log('翻译完成');
            return tempDiv.innerHTML;

        } catch (error) {
            console.error('翻译过程出错:', error);
            throw error;
        }
    }

    // 检测文本是否包含中文
    function containsChinese(text) {
        return /[\u4e00-\u9fff]/.test(text);
    }

    // 翻译单个文本的函数 - 只使用Google Translate免费API
    async function translateText(text) {
        try {
            // 只使用Google Translate免费API
            const googleTranslated = await translateWithGoogle(text);
            if (googleTranslated && googleTranslated !== text) {
                console.log('Google翻译成功:', text, '->', googleTranslated);
                return googleTranslated;
            }
        } catch (error) {
            console.warn('Google翻译失败:', error);
        }

        // 如果Google翻译失败，回退到本地词典
        console.log('Google翻译失败，使用本地词典翻译:', text);
        return translateWithLocalDictionary(text);
    }

    // Google Translate 免费API - 优化版本
    async function translateWithGoogle(text) {
        try {
            console.log('开始Google翻译:', text);

            // 使用多个Google Translate免费接口，提高成功率和速度
            const apis = [
                // 接口1：标准免费接口
                `https://translate.googleapis.com/translate_a/single?client=gtx&sl=zh&tl=en&dt=t&q=${encodeURIComponent(text)}`,
                // 接口2：备用接口
                `https://translate.google.com/translate_a/single?client=gtx&sl=zh&tl=en&dt=t&q=${encodeURIComponent(text)}`,
                // 接口3：移动端接口
                `https://translate.googleapis.com/translate_a/single?client=webapp&sl=zh&tl=en&dt=t&q=${encodeURIComponent(text)}`
            ];

            // 并发请求多个接口，使用最快返回的结果
            const promises = apis.map(async (url, index) => {
                try {
                    console.log(`尝试Google翻译接口${index + 1}:`, url.substring(0, 100) + '...');

                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), 8000); // 8秒超时

                    const response = await fetch(url, {
                        method: 'GET',
                        signal: controller.signal,
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    });

                    clearTimeout(timeoutId);

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}`);
                    }

                    const data = await response.json();
                    console.log(`接口${index + 1}响应:`, data);

                    if (data && data[0] && data[0][0] && data[0][0][0]) {
                        const result = data[0][0][0];
                        console.log(`接口${index + 1}翻译成功:`, text, '->', result);
                        return result;
                    }

                    throw new Error('响应格式不正确');
                } catch (error) {
                    console.warn(`接口${index + 1}失败:`, error.message);
                    throw error;
                }
            });

            // 使用Promise.any获取最快成功的结果
            try {
                const result = await Promise.any(promises);
                console.log('Google翻译最终结果:', result);
                return result;
            } catch (error) {
                console.warn('所有Google翻译接口都失败了:', error);
                throw new Error('所有翻译接口都不可用');
            }

        } catch (error) {
            console.warn('Google翻译请求失败:', error);
            throw error;
        }
    }

    // 本地词典翻译（备用方案）
    function translateWithLocalDictionary(text) {
        const dictionary = {
            // 常用技术术语
            '多屏异显': 'Multi-Screen Display',
            '数据采集': 'Data Collection',
            '网关': 'Gateway',
            '工具': 'Tools',
            '资源': 'Resources',
            '源代码': 'Source Code',
            '固件': 'Firmware',
            '文件系统': 'File System',
            '技术支持': 'Technical Support',
            '下载': 'Download',
            '安装': 'Installation',
            '配置': 'Configuration',
            '使用说明': 'User Manual',
            '操作步骤': 'Operation Steps',
            '注意事项': 'Precautions',
            '常见问题': 'FAQ',
            '故障排除': 'Troubleshooting',
            '系统要求': 'System Requirements',
            '硬件要求': 'Hardware Requirements',
            '软件要求': 'Software Requirements',
            '版本': 'Version',
            '更新': 'Update',
            '升级': 'Upgrade',
            '兼容性': 'Compatibility',
            '性能': 'Performance',
            '功能': 'Features',
            '特性': 'Features',
            '优势': 'Advantages',
            '应用': 'Applications',
            '解决方案': 'Solutions'
        };

        // 尝试完全匹配
        if (dictionary[text]) {
            return dictionary[text];
        }

        // 尝试部分匹配和替换
        let translatedText = text;
        for (let [chinese, english] of Object.entries(dictionary)) {
            translatedText = translatedText.replace(new RegExp(chinese, 'g'), english);
        }

        // 如果没有找到翻译，返回原文
        return translatedText;
    }

    // ==================== 翻译缓存管理 ====================

    // 生成缓存键
    function generateCacheKey(title, content) {
        // 使用标题和内容的哈希作为缓存键
        const combinedText = title + '|' + content.substring(0, 100); // 只取前100个字符避免键过长
        return 'translation_' + simpleHash(combinedText);
    }

    // 简单哈希函数
    function simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash).toString(36);
    }

    // 获取翻译缓存
    function getTranslationCache(cacheKey) {
        try {
            const cached = localStorage.getItem(cacheKey);
            if (cached) {
                const cacheData = JSON.parse(cached);
                // 检查缓存是否过期（7天）
                const now = Date.now();
                const cacheAge = now - cacheData.timestamp;
                const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天

                if (cacheAge < maxAge) {
                    console.log('找到有效的翻译缓存，缓存时间:', new Date(cacheData.timestamp).toLocaleString());
                    return cacheData.content;
                } else {
                    console.log('翻译缓存已过期，删除旧缓存');
                    localStorage.removeItem(cacheKey);
                }
            }
        } catch (error) {
            console.warn('读取翻译缓存失败:', error);
        }
        return null;
    }

    // 设置翻译缓存
    function setTranslationCache(cacheKey, content) {
        try {
            const cacheData = {
                content: content,
                timestamp: Date.now()
            };
            localStorage.setItem(cacheKey, JSON.stringify(cacheData));
            console.log('翻译内容已缓存，缓存键:', cacheKey);
        } catch (error) {
            console.warn('保存翻译缓存失败:', error);
            // 如果localStorage满了，清理旧的翻译缓存
            if (error.name === 'QuotaExceededError') {
                cleanOldTranslationCache();
                // 重试保存
                try {
                    localStorage.setItem(cacheKey, JSON.stringify(cacheData));
                } catch (retryError) {
                    console.error('重试保存缓存仍然失败:', retryError);
                }
            }
        }
    }

    // 清理旧的翻译缓存
    function cleanOldTranslationCache() {
        console.log('开始清理旧的翻译缓存...');
        const keysToRemove = [];

        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key && key.startsWith('translation_')) {
                try {
                    const cached = localStorage.getItem(key);
                    const cacheData = JSON.parse(cached);
                    const now = Date.now();
                    const cacheAge = now - cacheData.timestamp;
                    const maxAge = 3 * 24 * 60 * 60 * 1000; // 3天

                    if (cacheAge > maxAge) {
                        keysToRemove.push(key);
                    }
                } catch (error) {
                    // 如果解析失败，也删除这个键
                    keysToRemove.push(key);
                }
            }
        }

        keysToRemove.forEach(key => {
            localStorage.removeItem(key);
        });

        console.log('清理完成，删除了', keysToRemove.length, '个旧缓存');
    }

    // 预翻译并缓存（中文界面后台执行）
    async function preTranslateAndCache(title, htmlContent) {
        const cacheKey = generateCacheKey(title, htmlContent);

        // 检查是否已经有缓存
        if (getTranslationCache(cacheKey)) {
            console.log('翻译缓存已存在，跳过预翻译:', title);
            return;
        }

        console.log('🚀 开始预翻译内容:', title);
        console.log('原始内容长度:', htmlContent.length);

        try {
            const startTime = Date.now();

            // 在后台翻译内容
            const translatedContent = await translateContent(htmlContent);

            const endTime = Date.now();
            const duration = endTime - startTime;

            // 保存到缓存
            setTranslationCache(cacheKey, translatedContent);

            console.log('✅ 预翻译完成并已缓存:', title);
            console.log('翻译耗时:', duration + 'ms');
            console.log('翻译后内容长度:', translatedContent.length);
            console.log('翻译内容预览:', translatedContent.substring(0, 200) + '...');
            console.log('缓存键:', cacheKey);

            // 显示翻译统计
            showTranslationStats(title, htmlContent, translatedContent, duration);

        } catch (error) {
            console.error('❌ 预翻译失败:', title, error);
        }
    }

    // 显示翻译统计信息
    function showTranslationStats(title, originalContent, translatedContent, duration) {
        // 统计中文字符数
        const chineseChars = (originalContent.match(/[\u4e00-\u9fff]/g) || []).length;

        // 统计英文单词数
        const englishWords = translatedContent.split(/\s+/).filter(word => word.length > 0).length;

        console.group('📊 翻译统计 - ' + title);
        console.log('原始内容:', originalContent.length + ' 字符');
        console.log('中文字符:', chineseChars + ' 个');
        console.log('翻译后内容:', translatedContent.length + ' 字符');
        console.log('英文单词:', englishWords + ' 个');
        console.log('翻译速度:', Math.round(chineseChars / (duration / 1000)) + ' 字符/秒');
        console.log('翻译质量评估:', evaluateTranslationQuality(originalContent, translatedContent));
        console.groupEnd();
    }

    // 简单的翻译质量评估
    function evaluateTranslationQuality(original, translated) {
        const originalLength = original.length;
        const translatedLength = translated.length;
        const lengthRatio = translatedLength / originalLength;

        // 检查是否包含中文（翻译不完整）
        const hasUntranslatedChinese = /[\u4e00-\u9fff]/.test(translated);

        if (hasUntranslatedChinese) {
            return '⚠️ 部分未翻译';
        } else if (lengthRatio < 0.5) {
            return '⚠️ 翻译过短';
        } else if (lengthRatio > 2.0) {
            return '⚠️ 翻译过长';
        } else {
            return '✅ 翻译正常';
        }
    }

    // 批量预翻译所有菜单内容（中文界面启动时执行）
    function preTranslateAllContent() {
        console.log('🌟 开始批量预翻译所有菜单内容...');

        // 获取所有菜单项
        const menuItems = [];
        $('.menu-item').each(function() {
            const $menuItem = $(this);
            const itemText = $menuItem.find('a').text().trim();
            if (itemText) {
                menuItems.push(itemText);
            }
        });

        console.log('找到菜单项数量:', menuItems.length);
        console.log('菜单项列表:', menuItems);

        // 使用队列方式逐个翻译，避免同时发起太多请求
        let currentIndex = 0;
        const translateNext = () => {
            if (currentIndex >= menuItems.length) {
                console.log('🎉 所有菜单项预翻译任务已启动完成！');
                return;
            }

            const itemText = menuItems[currentIndex];
            console.log(`📝 准备预翻译第${currentIndex + 1}/${menuItems.length}项:`, itemText);

            // 获取对应的支持内容并预翻译
            preTranslateSupportContent(itemText).then(() => {
                currentIndex++;
                // 每个翻译任务间隔2秒，避免请求过于频繁
                setTimeout(translateNext, 2000);
            }).catch(error => {
                console.error('预翻译任务失败:', itemText, error);
                currentIndex++;
                setTimeout(translateNext, 2000);
            });
        };

        // 开始第一个翻译任务
        translateNext();
    }

    // 预翻译单个支持内容
    async function preTranslateSupportContent(title) {
        try {
            console.log('🔍 获取支持内容:', title);

            // 调用API获取支持内容
            const response = await $.ajax({
                type: "POST",
                url: "/apis/get_support_by_title/",
                data: {
                    title: title,
                    type: '详情'
                }
            });

            if (response.status === 'ok' && response.data && response.data.length > 0) {
                const supportData = response.data[0];
                let contentToDisplay = '';

                if (supportData.html_content && supportData.html_content.trim()) {
                    contentToDisplay = supportData.html_content;
                    console.log('✅ 获取到HTML内容，长度:', contentToDisplay.length);
                } else if (supportData.content && supportData.content.trim()) {
                    contentToDisplay = `<h2>${supportData.title}</h2>${supportData.content}`;
                    console.log('✅ 构建HTML内容，长度:', contentToDisplay.length);
                } else {
                    contentToDisplay = `<h2>${supportData.title}</h2><p>暂无详细内容</p>`;
                    console.log('⚠️ 使用默认内容');
                }

                // 预翻译并缓存
                await preTranslateAndCache(title, contentToDisplay);

            } else {
                console.warn('❌ 未找到支持内容:', title);
            }
        } catch (error) {
            console.error('❌ 预翻译支持内容失败:', title, error);
            throw error; // 重新抛出错误，让调用者处理
        }
    }

    // 在内容加载完成后加载页脚
    setTimeout(function() {
        loadFooter();

        // 如果是中文界面，启动预翻译
        const isEnglish = document.documentElement.lang === 'en' ||
                         window.location.pathname.includes('_en.html');

        if (!isEnglish) {
            console.log('检测到中文界面，启动预翻译任务');
            setTimeout(() => {
                preTranslateAllContent();
            }, 3000); // 延迟3秒后开始预翻译，确保页面完全加载
        }
    }, 1000); // 延迟1秒确保主要内容加载完成
});
