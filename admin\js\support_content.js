// 技术支持内容管理JavaScript

let currentPage = 1;
let pageSize = 10;
let currentEditId = null;
let allSupports = [];

// 页面加载完成后初始化
$(document).ready(function() {
    loadStatistics();
    loadSupports();
    bindEvents();
});

// 绑定事件
function bindEvents() {
    // 搜索框回车事件
    $('#search-title, #search-content').on('keypress', function(e) {
        if (e.which === 13) {
            searchSupports();
        }
    });
    
    // 状态筛选变化事件
    $('#status-filter').on('change', function() {
        searchSupports();
    });
}

// 加载统计信息
function loadStatistics() {
    console.log('加载统计信息...');

    $.ajax({
        url: '/apis/support_list/',
        type: 'GET',
        data: {
            type: '详情'  // 只获取详情类型的数据
        },
        success: function(response) {
            console.log('统计信息响应:', response);
            if (response.status === 'ok' && response.data) {
                const data = response.data;
                let totalSupports = data.length;
                let totalPublished = data.filter(item => item.show == 1).length;
                let totalDrafts = data.filter(item => item.show == 0).length;
                let recentUpdates = data.filter(item => {
                    const updateTime = new Date(item.update_time || item.created_at);
                    const weekAgo = new Date();
                    weekAgo.setDate(weekAgo.getDate() - 7);
                    return updateTime > weekAgo;
                }).length;

                $('#total-supports').text(totalSupports);
                $('#total-menus').text(totalPublished);  // 重新定义为已发布数量
                $('#total-details').text(totalDrafts);   // 重新定义为草稿数量
                $('#total-published').text(recentUpdates); // 重新定义为最近更新数量

                console.log('统计信息更新完成:', {
                    totalSupports,
                    totalPublished,
                    totalDrafts,
                    recentUpdates
                });
            } else {
                console.error('统计信息加载失败:', response);
                showMessage('统计信息加载失败: ' + (response.message || response.msg || '未知错误'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('统计信息AJAX请求失败:', error);
            console.error('状态:', status);
            console.error('响应:', xhr.responseText);
            showMessage('加载统计信息失败: ' + error, 'error');
        }
    });
}

// 加载支持内容列表
function loadSupports(page = 1) {
    currentPage = page;

    console.log('加载支持内容列表，页码:', page);

    $.ajax({
        url: '/apis/support_list/',
        type: 'GET',
        data: {
            page: page,
            pageSize: pageSize,
            type: '详情'  // 只获取详情类型的数据
        },
        success: function(response) {
            console.log('支持内容列表响应:', response);
            if (response.status === 'ok' && response.data) {
                allSupports = response.data;
                renderSupportsList(response.data);
                renderPagination(response.total || response.data.length);
            } else {
                console.error('加载支持内容失败:', response);
                showMessage('加载失败: ' + (response.message || response.msg || '未知错误'), 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('加载支持内容AJAX请求失败:', error);
            console.error('状态:', status);
            console.error('响应:', xhr.responseText);
            showMessage('加载支持内容列表失败: ' + error, 'error');
        }
    });
}

// 渲染支持内容列表
function renderSupportsList(supports) {
    const tbody = $('#supports-list');
    tbody.empty();
    
    if (!supports || supports.length === 0) {
        tbody.append('<tr><td colspan="8" class="text-center">暂无数据</td></tr>');
        return;
    }
    
    supports.forEach(function(support) {
        const row = `
            <tr>
                <td>${support.id}</td>
                <td>${support.title || '-'}</td>
                <td><div class="content-preview" title="${support.content || ''}">${truncateText(support.content || '', 50)}</div></td>
                <td>${support.display_order || 0}</td>
                <td><span class="status-label ${getStatusClass(support.show)}">${getStatusText(support.show)}</span></td>
                <td>${formatDate(support.created_at || support.update_time)}</td>
                <td class="action-buttons">
                    <button class="btn btn-mini btn-info" onclick="viewSupport(${support.id})" title="查看详情">
                        <i class="icon-eye-open icon-white"></i>
                    </button>
                    <button class="btn btn-mini btn-warning" onclick="editSupport(${support.id})" title="编辑">
                        <i class="icon-edit icon-white"></i>
                    </button>
                    <button class="btn btn-mini btn-success" onclick="openEditor(${support.id})" title="内容编辑器">
                        <i class="icon-pencil icon-white"></i>
                    </button>
                    <button class="btn btn-mini btn-primary" onclick="previewContent(${support.id})" title="预览">
                        <i class="icon-search icon-white"></i>
                    </button>
                    <button class="btn btn-mini btn-danger" onclick="deleteSupport(${support.id})" title="删除">
                        <i class="icon-trash icon-white"></i>
                    </button>
                </td>
            </tr>
        `;
        tbody.append(row);
    });
}

// 渲染分页
function renderPagination(total) {
    const totalPages = Math.ceil(total / pageSize);
    const pagination = $('#pagination');
    pagination.empty();
    
    if (totalPages <= 1) return;
    
    // 上一页
    if (currentPage > 1) {
        pagination.append(`<a href="javascript:loadSupports(${currentPage - 1})" class="fg-button ui-button ui-state-default">上一页</a>`);
    }
    
    // 页码
    for (let i = 1; i <= totalPages; i++) {
        const activeClass = i === currentPage ? 'ui-state-active' : '';
        pagination.append(`<a href="javascript:loadSupports(${i})" class="fg-button ui-button ui-state-default ${activeClass}">${i}</a>`);
    }
    
    // 下一页
    if (currentPage < totalPages) {
        pagination.append(`<a href="javascript:loadSupports(${currentPage + 1})" class="fg-button ui-button ui-state-default">下一页</a>`);
    }
}

// 搜索支持内容
function searchSupports() {
    const title = $('#search-title').val().trim();
    const content = $('#search-content').val().trim();
    const status = $('#status-filter').val(); // 改为状态筛选

    let filteredSupports = allSupports;

    if (title) {
        filteredSupports = filteredSupports.filter(support =>
            (support.title || '').toLowerCase().includes(title.toLowerCase())
        );
    }

    if (content) {
        filteredSupports = filteredSupports.filter(support =>
            (support.content || '').toLowerCase().includes(content.toLowerCase())
        );
    }

    if (status) {
        filteredSupports = filteredSupports.filter(support => support.show == status);
    }

    renderSupportsList(filteredSupports);
    renderPagination(filteredSupports.length);
}

// 重置搜索
function resetSearch() {
    $('#search-title').val('');
    $('#search-content').val('');
    $('#status-filter').val('');
    loadSupports(1);
}

// 新增支持内容
function createNewSupport() {
    currentEditId = null;
    $('#edit-modal-title').text('新增支持内容');
    $('#support-form')[0].reset();
    $('#support-type').val('详情').prop('disabled', true); // 固定为详情类型且不可修改
    $('#support-show').val('1');
    $('#editSupportModal').modal('show');
}

// 查看支持内容详情
function viewSupport(id) {
    const support = allSupports.find(s => s.id === id);
    if (!support) {
        showMessage('找不到该支持内容', 'error');
        return;
    }
    
    const detailHtml = `
        <div class="row-fluid">
            <div class="span6"><strong>ID:</strong> ${support.id}</div>
            <div class="span6"><strong>类型:</strong> 详情内容</div>
        </div>
        <div class="row-fluid">
            <div class="span12"><strong>标题:</strong> ${support.title || '-'}</div>
        </div>
        <div class="row-fluid">
            <div class="span12"><strong>内容:</strong><br><div style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin-top: 5px;">${support.content || '-'}</div></div>
        </div>
        <div class="row-fluid">
            <div class="span6"><strong>显示顺序:</strong> ${support.display_order || 0}</div>
            <div class="span6"><strong>状态:</strong> ${getStatusText(support.show)}</div>
        </div>
        <div class="row-fluid">
            <div class="span12"><strong>链接URL:</strong> ${support.url || '-'}</div>
        </div>
        <div class="row-fluid">
            <div class="span12"><strong>创建时间:</strong> ${formatDate(support.created_at || support.update_time)}</div>
        </div>
    `;
    
    $('#support-detail-content').html(detailHtml);
    currentEditId = id;
    $('#supportDetailModal').modal('show');
}

// 编辑支持内容
function editSupport(id) {
    const support = allSupports.find(s => s.id === id);
    if (!support) {
        showMessage('找不到该支持内容', 'error');
        return;
    }
    
    currentEditId = id;
    $('#edit-modal-title').text('编辑支持内容');
    $('#support-type').val('详情').prop('disabled', true); // 固定为详情类型且不可修改
    $('#support-title').val(support.title || '');
    $('#support-content').val(support.content || '');
    $('#support-order').val(support.display_order || 1);
    $('#support-url').val(support.url || '');
    $('#support-show').val(support.show || 1);

    $('#editSupportModal').modal('show');
}

// 打开内容编辑器（支持通过ID和标题两种方式，参考微信文章编辑器）
function openEditor(id) {
    if (!id || isNaN(id)) {
        showMessage('无效的内容ID', 'error');
        return;
    }

    console.log('打开编辑器，ID:', id);

    // 查找对应的支持内容数据，获取标题
    const support = allSupports.find(s => s.id == id);
    if (support && support.title) {
        console.log('找到支持内容标题:', support.title);

        // 优先使用标题方式（参考微信文章编辑器）
        const encodedTitle = encodeURIComponent(support.title);

        // 保存标题到Cookie以便编辑器使用
        $.cookie('support_content_title', support.title, { expires: 1 });
        // 清除旧的ID Cookie
        $.removeCookie('support_content_id');

        // 跳转到h5文件夹中的编辑器页面（使用标题参数）
        window.open(`/h5/support_editor.html?title=${encodedTitle}`, '_blank');
    } else {
        // 如果没有标题，则使用ID方式
        console.log('未找到标题，使用ID方式');

        // 保存ID到Cookie以便编辑器使用
        $.cookie('support_content_id', id, { expires: 1 });

        // 跳转到h5文件夹中的编辑器页面
        window.open(`/h5/support_editor.html?id=${id}`, '_blank');
    }
}

// 预览内容
function previewContent(id) {
    // 跳转到预览页面
    window.open(`/h5/support_preview.html?id=${id}`, '_blank');
}

// 保存支持内容
function saveSupport() {
    const formData = {
        type: '详情', // 固定为详情类型
        title: $('#support-title').val().trim(),
        content: $('#support-content').val().trim(),
        display_order: parseInt($('#support-order').val()) || 1,
        url: $('#support-url').val().trim(),
        show: parseInt($('#support-show').val()),
        lang: 0 // 默认中文
    };
    
    if (!formData.title) {
        showMessage('请输入标题', 'error');
        return;
    }
    
    // 使用真实的API保存到数据库
    const url = currentEditId ? `/apis/update_support/` : '/apis/add_support/';
    const method = currentEditId ? 'POST' : 'POST';

    // 如果是更新，添加ID到数据中
    if (currentEditId) {
        formData.id = currentEditId;
    }

    console.log('保存数据到数据库:', formData);
    console.log('API URL:', url);
    console.log('Method:', method);

    $.ajax({
        url: url,
        type: method,
        data: formData, // 直接发送表单数据，不用JSON.stringify
        success: function(response) {
            console.log('服务器响应:', response);
            if (response.status === 'ok') {
                showMessage(currentEditId ? '更新成功' : '添加成功', 'success');
                $('#editSupportModal').modal('hide');
                loadSupports(currentPage);
                loadStatistics();
            } else {
                showMessage('保存失败: ' + (response.message || response.msg || '未知错误'), 'error');
                console.error('保存失败:', response);
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX请求失败:', error);
            console.error('状态:', status);
            console.error('响应:', xhr.responseText);
            showMessage('保存失败: ' + error, 'error');
        }
    });
}

// 删除支持内容
function deleteSupport(id) {
    if (!confirm('确定要删除这个支持内容吗？')) {
        return;
    }

    console.log('删除支持内容，ID:', id);

    $.ajax({
        url: `/apis/delete_support/`,
        type: 'POST',
        data: { id: id },
        success: function(response) {
            console.log('删除响应:', response);
            if (response.status === 'ok') {
                showMessage('删除成功', 'success');
                loadSupports(currentPage);
                loadStatistics();
            } else {
                showMessage('删除失败: ' + (response.message || response.msg || '未知错误'), 'error');
                console.error('删除失败:', response);
            }
        },
        error: function(xhr, status, error) {
            console.error('删除AJAX请求失败:', error);
            console.error('状态:', status);
            console.error('响应:', xhr.responseText);
            showMessage('删除失败: ' + error, 'error');
        }
    });
}

// 工具函数
function truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

function getStatusClass(show) {
    return show == 1 ? 'status-published' : 'status-hidden';
}

function getStatusText(show) {
    return show == 1 ? '已发布' : '已隐藏';
}

function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function showMessage(message, type) {
    const gritterType = type === 'success' ? 'success' : 'error';
    $.gritter.add({
        title: type === 'success' ? '成功' : '错误',
        text: message,
        class_name: gritterType,
        time: 3000
    });
}

// 退出登录
function logout() {
    if (confirm('确定要退出登录吗？')) {
        sessionStorage.removeItem('adminLoggedIn');
        window.location.href = '/login/login.html';
    }
}
