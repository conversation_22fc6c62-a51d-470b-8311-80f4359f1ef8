// 资料下载详情管理JS
var currentPage = 1;
var pageSize = 20; // 固定每页显示20条
var totalPage = 0;
var currentLang = '';
var currentType = '';
var allTypes = [];

// 渲染文件预览（支持图片和PDF）
function renderFilePreview(filePath) {
    if (!filePath) return '';

    var isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(filePath);
    var isPDF = /\.pdf$/i.test(filePath);

    if (isImage) {
        return '<img src="' + filePath + '" class="download-image"/>';
    } else if (isPDF) {
        var fileName = filePath.split('/').pop();
        return '<div style="padding:5px;border:1px solid #ccc;background:#f5f5f5;text-align:center;max-width:120px;">' +
               '<i class="icon-file-text" style="font-size:16px;color:#d9534f;"></i><br/>' +
               '<small style="font-size:10px;">' + fileName + '</small>' +
               '</div>';
    } else {
        // 其他文件类型，显示为通用文件图标
        var fileName = filePath.split('/').pop();
        return '<div style="padding:5px;border:1px solid #ccc;background:#f5f5f5;text-align:center;max-width:120px;">' +
               '<i class="icon-file" style="font-size:16px;color:#999;"></i><br/>' +
               '<small style="font-size:10px;">' + fileName + '</small>' +
               '</div>';
    }
}

function fetchTypesAndFill() {
    // 记住当前选中的类型
    var currentType = $('#type_filter').val() || '';
    $.get('/apis/download_detail_types/', function(res) {
        if(res.status === 'ok' && Array.isArray(res.data)) {
            var allTypes = res.data;
            var $typeFilter = $('#type_filter');
            $typeFilter.empty().append('<option value="">所有分类</option>');
            allTypes.forEach(function(type) {
                $typeFilter.append('<option value="'+type+'">'+type+'</option>');
            });
            // 恢复选中项
            $typeFilter.val(currentType);
            // 填充datalist
            var $typeList = $('#type_list, #type_list_add');
            $typeList.empty();
            allTypes.forEach(function(type) {
                $typeList.append('<option value="'+type+'">');
            });
        }
    });
}

function detail_list(page, size, lang, type) {
    currentPage = page || 1;
    pageSize = 20; // 强制使用20条每页
    currentLang = lang || '';
    currentType = type || '';
    
    var params = { 
        page: currentPage, 
        size: pageSize
    };
    if (currentLang !== '') params.lang = currentLang;
    if (currentType !== '') params.type = currentType;
    
    $.ajax({
        type: "get",
        url: "/apis/download_detail_list/",
        data: params,
        headers: { 'X-Client-Type': 'admin' },
        success: function (data) {
            if (data.status === 'ok') {
                render_detail_list(data.data);
                totalPage = Math.ceil(data.total / pageSize);
                page_ctrl(currentPage, totalPage);
                fetchTypesAndFill(); // 每次加载后刷新类型
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '加载失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，加载失败', sticky: false, time: 3000 });
        }
    });
}

function render_detail_list(data) {
    var html = '';
    if (!data || data.length === 0) {
        html = '<tr><td colspan="11" class="text-center">暂无数据</td></tr>';
    } else {
        $.each(data, function (index, item) {
            var langText = item.lang == 0 ? '<span class="label label-success">中文</span>' : '<span class="label label-info">英文</span>';
            var bg_style = item.lang == 0 ? ' style="background-color:#d2d2f7"' : '';
            var isShow = item.show === true || item.show === 1 || item.show === "1";
            var showText = isShow ? '<span class="badge badge-success toggle-show-status" data-id="' + item.id + '" data-show="1" style="cursor:pointer">显示</span>' : '<span class="badge badge-important toggle-show-status" data-id="' + item.id + '" data-show="0" style="cursor:pointer">不显示</span>';
            var updateTime = item.update_time ? new Date(item.update_time).toLocaleString('zh-CN', { hour12: false, timeZone: 'Asia/Shanghai' }) : '';
            html += '<tr>' +
                '<td' + bg_style + '>' + item.id + '</td>' +
                '<td' + bg_style + '>' + langText + '</td>' +
                '<td' + bg_style + '>' + (item.type || '') + '</td>' +
                '<td' + bg_style + '>' + (item.title || '') + '</td>' +
                '<td' + bg_style + '>' + (item.content || '') + '</td>' +
                '<td' + bg_style + '>' + renderFilePreview(item.image_path) + '</td>' +
                '<td' + bg_style + '>' + (item.display_order || 100) + '</td>' +
                '<td' + bg_style + '>' + showText + '</td>' +
                '<td' + bg_style + '>' + updateTime + '</td>' +
                '<td' + bg_style + '>' + (item.url || '') + '</td>' +
                '<td' + bg_style + '>' +
                '<button class="btn btn-primary btn-mini" onclick="show_edit_modal(' + item.id + ')">编辑</button> ' +
                '<button class="btn btn-danger btn-mini" onclick="show_delete_modal(' + item.id + ')">删除</button>' +
                '</td>' +
                '</tr>';
        });
    }
    $("#data_list").html(html);
    $('.toggle-show-status').off('click').on('click', function(e) {
        e.preventDefault(); e.stopPropagation();
        var id = $(this).data('id');
        var currentShow = $(this).data('show');
        var newShow = currentShow == 1 ? false : true;
        toggle_show_status(id, newShow);
    });
}

// 清除编辑框中的文件（图片或PDF）
function clearEditImage() {
    $('#edit_image_path').val('');
    $('#edit_image_preview').attr('src', '').hide();
    $('#edit_pdf_preview').hide();
    $('#edit_image_file').val(''); // 清除文件输入框
}

// 清除添加框中的文件（图片或PDF）
function clearAddImage() {
    $('#add_image_path').val('');
    $('#add_image_preview').attr('src', '').hide();
    $('#add_pdf_preview').hide();
    $('#add_image_file').val(''); // 清除文件输入框
}

// 重置添加表单时也要清除文件
function resetAddForm() {
    $('#add_type').val('');
    $('#add_title').val('');
    $('#add_content').val('');
    $('#add_display_order').val('100');
    $('#add_url').val('');
    $('#add_show').val('True');
    $('#add_lang').val('0');
    clearAddImage(); // 清除文件
}

function add_detail_modal_show() {
    resetAddForm();
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('#addDetailAlert').modal('show');
    setTimeout(function() { $('#addDetailAlert').focus(); }, 200);
}

// 分页控制函数，仿照download_nav.js
function page_ctrl(currentPage, totalPage) {
    var html = "";
    
    // 分页按钮
    html += "<a tabindex='0' class='first ui-corner-tl ui-corner-bl fg-button ui-button ui-state-default " + 
        (currentPage === 1 ? "ui-state-disabled" : "") + 
        "' onclick='detail_list(1, " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>首页</a>";
    
    html += "<a tabindex='0' class='previous fg-button ui-button ui-state-default " + 
        (currentPage === 1 ? "ui-state-disabled" : "") + 
        "' onclick='detail_list(" + (currentPage - 1) + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>上一页</a>";
    
    var startPage = Math.max(1, currentPage - 2);
    var endPage = Math.min(totalPage, startPage + 4);
    
    for (var i = startPage; i <= endPage; i++) {
        html += "<a tabindex='0' class='fg-button ui-button ui-state-default " + 
            (i === currentPage ? "ui-state-disabled" : "") + 
            "' onclick='detail_list(" + i + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>" + i + "</a>";
    }
    
    html += "<a tabindex='0' class='next fg-button ui-button ui-state-default " + 
        (currentPage === totalPage || totalPage === 0 ? "ui-state-disabled" : "") + 
        "' onclick='detail_list(" + (currentPage + 1) + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>下一页</a>";
    
    html += "<a tabindex='0' class='last ui-corner-tr ui-corner-br fg-button ui-button ui-state-default " + 
        (currentPage === totalPage || totalPage === 0 ? "ui-state-disabled" : "") + 
        "' onclick='detail_list(" + totalPage + ", " + pageSize + ", \"" + currentLang + "\", \"" + currentType + "\")'>尾页</a>";
    
    $("#page").html(html);
}

// 兼容Bootstrap 2.x弹框，修复遮罩层残留、事件失效等
$(document).ready(function() {
    // 加载列表数据，固定20条每页
    detail_list(1, 20);
    fetchTypesAndFill();

    // 绑定筛选事件
    $('#lang_filter').change(function() {
        currentLang = $(this).val();
        detail_list(1, 20, currentLang, currentType); // 重置到第一页，保持20条每页
    });
    
    $('#type_filter').change(function() {
        currentType = $(this).val();
        detail_list(1, 20, currentLang, currentType); // 重置到第一页，保持20条每页
    });

    // 文件上传预览（支持图片和PDF）
    $(document).on('change', '#add_image_file', function() {
        var file = this.files[0];
        if (!file) return;

        // 检查文件类型
        var fileType = file.type;
        var fileName = file.name;
        var isImage = fileType.startsWith('image/');
        var isPDF = fileType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf');

        if (!isImage && !isPDF) {
            $.gritter.add({ title: '错误', text: '只支持图片文件和PDF文件', sticky: false, time: 3000 });
            return;
        }

        var formData = new FormData();
        formData.append('file', file);

        // 根据文件类型选择上传接口
        var uploadUrl = isImage ? '/apis/upload_pic/' : '/apis/upload_file/';

        $.ajax({
            url: uploadUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
                if(res.status === 'ok' && res.path) {
                    $('#add_image_path').val(res.path);

                    // 根据文件类型显示不同的预览
                    if (isImage) {
                        $('#add_pdf_preview').hide();
                        $('#add_image_preview').attr('src', res.path).show();
                    } else if (isPDF) {
                        $('#add_image_preview').hide();
                        $('#add_pdf_name').text(fileName);
                        $('#add_pdf_preview').show();
                    }
                } else {
                    $.gritter.add({ title: '错误', text: res.msg || '文件上传失败', sticky: false, time: 3000 });
                }
            },
            error: function() {
                $.gritter.add({ title: '错误', text: '文件上传失败', sticky: false, time: 3000 });
            }
        });
    });
    $(document).on('change', '#edit_image_file', function() {
        var file = this.files[0];
        if (!file) return;

        // 检查文件类型
        var fileType = file.type;
        var fileName = file.name;
        var isImage = fileType.startsWith('image/');
        var isPDF = fileType === 'application/pdf' || fileName.toLowerCase().endsWith('.pdf');

        if (!isImage && !isPDF) {
            $.gritter.add({ title: '错误', text: '只支持图片文件和PDF文件', sticky: false, time: 3000 });
            return;
        }

        var formData = new FormData();
        formData.append('file', file);

        // 根据文件类型选择上传接口
        var uploadUrl = isImage ? '/apis/upload_pic/' : '/apis/upload_file/';

        $.ajax({
            url: uploadUrl,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(res) {
                if(res.status === 'ok' && res.path) {
                    $('#edit_image_path').val(res.path);

                    // 根据文件类型显示不同的预览
                    if (isImage) {
                        $('#edit_pdf_preview').hide();
                        $('#edit_image_preview').attr('src', res.path).show();
                    } else if (isPDF) {
                        $('#edit_image_preview').hide();
                        $('#edit_pdf_name').text(fileName);
                        $('#edit_pdf_preview').show();
                    }
                } else {
                    $.gritter.add({ title: '错误', text: res.msg || '文件上传失败', sticky: false, time: 3000 });
                }
            },
            error: function() {
                $.gritter.add({ title: '错误', text: '文件上传失败', sticky: false, time: 3000 });
            }
        });
    });
    // 修复 hidden 事件监听 - 使用正确的事件名称
    $(document).on('hidden', '.modal', function() {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('padding-right', '');
    });
    // 初始清理
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    // 确保编辑和删除按钮的事件处理
    $(document).on('click', '.btn-primary.btn-mini', function() {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });
    $(document).on('click', '.btn-danger.btn-mini', function() {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
    });

    // 添加弹窗清除文件按钮
    $('#add_clear_image').on('click', function() {
        $('#add_image_preview').attr('src', '').hide();
        $('#add_pdf_preview').hide();
        $('#add_image_file').val('');
        $('#add_image_path').val('');
    });

    // 编辑弹窗清除文件按钮
    $('#edit_clear_image').on('click', function() {
        $('#edit_image_preview').attr('src', '').hide();
        $('#edit_pdf_preview').hide();
        $('#edit_image_file').val('');
        $('#edit_image_path').val('');
    });
});

// 编辑弹框显示
function show_edit_modal(id) {
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $.ajax({
        type: "get",
        url: "/apis/download_detail_detail/",
        data: { id: id },
        success: function (data) {
            if (data.status === 'ok') {
                var item = data.data;
                $('#edit_id').val(item.id);
                $('#edit_type').val(item.type);
                $('#edit_title').val(item.title || '');
                $('#edit_content').val(item.content || '');
                $('#edit_display_order').val(item.display_order || 100);
                $('#edit_lang').val(item.lang);
                $('#edit_show').val(item.show ? "True" : "False");
                $('#edit_url').val(item.url || '');
                $('#edit_image_path').val(item.image_path || '');
                if(item.image_path) {
                    // 检查文件类型
                    var filePath = item.image_path;
                    var isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(filePath);
                    var isPDF = /\.pdf$/i.test(filePath);

                    if (isImage) {
                        $('#edit_pdf_preview').hide();
                        $('#edit_image_preview').attr('src', filePath).show();
                    } else if (isPDF) {
                        $('#edit_image_preview').hide();
                        var fileName = filePath.split('/').pop();
                        $('#edit_pdf_name').text(fileName);
                        $('#edit_pdf_preview').show();
                    } else {
                        // 其他文件类型，默认显示为图片
                        $('#edit_pdf_preview').hide();
                        $('#edit_image_preview').attr('src', filePath).show();
                    }
                } else {
                    $('#edit_image_preview').hide();
                    $('#edit_pdf_preview').hide();
                }
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                $('#editDetailAlert').modal('show');
                setTimeout(function(){ $('#editDetailAlert').focus(); }, 200);
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '获取详情失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，获取详情失败', sticky: false, time: 3000 });
        }
    });
}

// 删除弹框显示
function show_delete_modal(id) {
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $.ajax({
        type: "get",
        url: "/apis/download_detail_detail/",
        data: { id: id },
        success: function (data) {
            if (data.status === 'ok') {
                var item = data.data;
                $('#del_id').val(item.id);
                $('#del_id_span').text(item.id);
                $('#del_title').text(item.title || '');
                $('#del_content').text(item.content || '');
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
                $('#deleteDetailAlert').modal('show');
                setTimeout(function(){ $('#deleteDetailAlert').focus(); }, 200);
            } else {
                $.gritter.add({ title: '错误', text: data.msg || '获取详情失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，获取详情失败', sticky: false, time: 3000 });
        }
    });
}

// 添加资料详情
function add_detail() {
    var type = $('#add_type').val();
    var title = $('#add_title').val();
    var content = $('#add_content').val();
    var display_order = $('#add_display_order').val() || 100;
    var lang = $('#add_lang').val();
    var show = $('#add_show').val() === 'True';
    var url = $('#add_url').val();
    var image_path = $('#add_image_path').val();
    
    if (!type) {
        $.gritter.add({ title: '错误', text: '请填写分类类型', sticky: false, time: 3000 });
        return;
    }
    
    if (!title) {
        $.gritter.add({ title: '错误', text: '请填写标题', sticky: false, time: 3000 });
        return;
    }
    
    var data = {
        type: type,
        title: title,
        content: content,
        display_order: display_order,
        lang: lang,
        show: show,
        url: url,
        image_path: image_path
    };
    
    $.ajax({
        type: "post",
        url: "/apis/add_download_detail/",
        data: JSON.stringify(data),
        contentType: "application/json",
        success: function (res) {
            if (res.status === 'ok') {
                $.gritter.add({ title: '成功', text: '添加资料详情成功', sticky: false, time: 3000 });
                $('#addDetailAlert').modal('hide');
                detail_list(currentPage, pageSize, currentLang, currentType);
            } else {
                $.gritter.add({ title: '错误', text: res.msg || '添加资料详情失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，添加资料详情失败', sticky: false, time: 3000 });
        }
    });
}

// 编辑资料详情
function edit_detail() {
    var id = $('#edit_id').val();
    var type = $('#edit_type').val();
    var title = $('#edit_title').val();
    var content = $('#edit_content').val();
    var display_order = $('#edit_display_order').val() || 100;
    var lang = $('#edit_lang').val();
    var show = $('#edit_show').val() === 'True';
    var url = $('#edit_url').val();
    var image_path = $('#edit_image_path').val();
    
    if (!type) {
        $.gritter.add({ title: '错误', text: '请填写分类类型', sticky: false, time: 3000 });
        return;
    }
    
    if (!title) {
        $.gritter.add({ title: '错误', text: '请填写标题', sticky: false, time: 3000 });
        return;
    }
    
    var data = {
        id: id,
        type: type,
        title: title,
        content: content,
        display_order: display_order,
        lang: lang,
        show: show,
        url: url,
        image_path: image_path
    };
    
    $.ajax({
        type: "post",
        url: "/apis/update_download_detail/",
        data: JSON.stringify(data),
        contentType: "application/json",
        success: function (res) {
            if (res.status === 'ok') {
                $.gritter.add({ title: '成功', text: '编辑资料详情成功', sticky: false, time: 3000 });
                $('#editDetailAlert').modal('hide');
                detail_list(currentPage, pageSize, currentLang, currentType);
            } else {
                $.gritter.add({ title: '错误', text: res.msg || '编辑资料详情失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，编辑资料详情失败', sticky: false, time: 3000 });
        }
    });
}

// 删除资料详情
function del_detail() {
    var id = $('#del_id').val();
    
    $.ajax({
        type: "post",
        url: "/apis/delete_download_detail/",
        data: JSON.stringify({ id: id }),
        contentType: "application/json",
        success: function (res) {
            if (res.status === 'ok') {
                $.gritter.add({ title: '成功', text: '删除资料详情成功', sticky: false, time: 3000 });
                $('#deleteDetailAlert').modal('hide');
                detail_list(currentPage, pageSize, currentLang, currentType);
            } else {
                $.gritter.add({ title: '错误', text: res.msg || '删除资料详情失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，删除资料详情失败', sticky: false, time: 3000 });
        }
    });
}

// 生成下载页面函数
function generateDownloadPages() {
    // 获取中英文产品名
    var productNameZh = $('#generate_product_name_zh').val().trim();
    var productNameEn = $('#generate_product_name_en').val().trim();

    // 验证输入
    if (!productNameZh || !productNameEn) {
        $.gritter.add({
            title: '错误',
            text: '请输入中文和英文产品名称',
            class_name: 'gritter-error'
        });
        return;
    }

    // 显示加载提示
    $.gritter.add({
        title: '提示',
        text: '正在生成页面，请稍候...',
        class_name: 'gritter-info'
    });

    // 发送请求到服务器
    $.ajax({
        url: '/apis/generate_download_pages',
        type: 'POST',
        data: {
            productNameZh: productNameZh,
            productNameEn: productNameEn
        },
        success: function(response) {
            if (response.status === 'ok') {
                $('#generatePageAlert').modal('hide');
                $.gritter.add({
                    title: '成功',
                    text: '下载页面生成成功！',
                    class_name: 'gritter-success'
                });
                // 清空输入框
                $('#generate_product_name_zh').val('');
                $('#generate_product_name_en').val('');
            } else {
                $.gritter.add({
                    title: '错误',
                    text: response.message || '生成页面失败',
                    class_name: 'gritter-error'
                });
            }
        },
        error: function() {
            $.gritter.add({
                title: '错误',
                text: '服务器错误，请稍后重试',
                class_name: 'gritter-error'
            });
        }
    });
}

// 切换显示状态
function toggle_show_status(id, newShow) {
    $.ajax({
        type: "post",
        url: "/apis/toggle_download_detail_show/",
        data: JSON.stringify({ id: id, show: newShow }),
        contentType: "application/json",
        success: function (res) {
            if (res.status === 'ok') {
                $.gritter.add({ title: '成功', text: '切换显示状态成功', sticky: false, time: 1500 });
                detail_list(currentPage, pageSize, currentLang, currentType);
            } else {
                $.gritter.add({ title: '错误', text: res.msg || '切换显示状态失败', sticky: false, time: 3000 });
            }
        },
        error: function () {
            $.gritter.add({ title: '错误', text: '网络错误，切换显示状态失败', sticky: false, time: 3000 });
        }
    });
}

// 手动强制刷新缓存（供按钮调用）
function force_refresh_download_cache() {
    console.log("开始发送刷新信号...");

    // 先检查是否已存在固定的刷新信号记录
    $.ajax({
        type: "get",
        url: "/apis/download_detail_list/",
        data: {
            type: 'CACHE_REFRESH_SIGNAL',
            lang: 0,
            page: 1,
            size: 10
        },
        success: function(response) {
            console.log("检查刷新信号记录响应:", response);

            // 在结果中查找固定标题的记录
            var existingRecord = null;
            if (response && response.status === 'ok' && response.data && response.data.length > 0) {
                existingRecord = response.data.find(item => item.title === 'cache_refresh_signal');
            }

            if (existingRecord) {
                // 已存在固定记录，更新其时间戳
                console.log("找到固定刷新信号记录，准备更新时间戳:", existingRecord);

                $.ajax({
                    type: "post",
                    url: "/apis/update_download_detail/",
                    data: JSON.stringify({
                        id: existingRecord.id,
                        type: 'CACHE_REFRESH_SIGNAL',
                        title: 'cache_refresh_signal',  // 保持固定标题
                        content: 'Last refresh: ' + new Date().toLocaleString(),
                        display_order: 9999,
                        lang: 0,
                        show: false,
                        url: '',
                        image_path: ''
                    }),
                    contentType: "application/json",
                    success: function(data) {
                        console.log("更新固定刷新信号响应:", data);
                        if (data && data.status === 'ok') {
                            $.gritter.add({ title: '成功', text: '已通知前端强制刷新缓存！', sticky: false, time: 3000 });
                            console.log("固定刷新信号时间戳已更新，ID:", existingRecord.id);
                        } else {
                            $.gritter.add({ title: '错误', text: '更新刷新信号失败', sticky: false, time: 3000 });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log("更新固定刷新信号失败:", {status, error, responseText: xhr.responseText});
                        $.gritter.add({ title: '错误', text: '网络错误，更新刷新信号失败', sticky: false, time: 3000 });
                    }
                });
            } else {
                // 不存在，创建固定的刷新信号记录
                console.log("未找到固定刷新信号记录，准备创建");

                $.ajax({
                    type: "post",
                    url: "/apis/add_download_detail/",
                    data: JSON.stringify({
                        type: 'CACHE_REFRESH_SIGNAL',
                        title: 'cache_refresh_signal',  // 固定标题
                        content: 'Last refresh: ' + new Date().toLocaleString(),
                        display_order: 9999,
                        lang: 0,
                        show: false,
                        url: '',
                        image_path: ''
                    }),
                    contentType: "application/json",
                    success: function(data) {
                        console.log("创建固定刷新信号响应:", data);
                        if (data && data.status === 'ok') {
                            $.gritter.add({ title: '成功', text: '已通知前端强制刷新缓存！', sticky: false, time: 3000 });
                            console.log("固定刷新信号已创建，ID:", data.id);
                        } else {
                            $.gritter.add({ title: '错误', text: '创建刷新信号失败', sticky: false, time: 3000 });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.log("创建固定刷新信号失败:", {status, error, responseText: xhr.responseText});
                        $.gritter.add({ title: '错误', text: '网络错误，创建刷新信号失败', sticky: false, time: 3000 });
                    }
                });
            }
        },
        error: function(xhr, status, error) {
            console.log("检查固定刷新信号失败:", {status, error, responseText: xhr.responseText});
            $.gritter.add({ title: '错误', text: '网络错误，检查刷新信号失败', sticky: false, time: 3000 });
        }
    });
}