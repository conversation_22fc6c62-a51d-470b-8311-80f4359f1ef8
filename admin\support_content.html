<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <title>技术支持内容管理</title>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="app-type" content="admin-panel" />
    <meta name="client-type" content="admin" />
    <link rel="stylesheet" href="css/bootstrap.min.css"/>
    <link rel="stylesheet" href="css/bootstrap-responsive.min.css"/>
    <link rel="stylesheet" href="css/matrix-style.css"/>
    <link rel="stylesheet" href="css/matrix-media.css"/>
    <link href="font-awesome/css/font-awesome.css" rel="stylesheet"/>
    <link rel="stylesheet" href="css/jquery.gritter.css"/>
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.cookie.js"></script>
    <style>
        #wrapper { display: flex; min-height: 100vh; }
        #content { flex: 1; margin-left: 0 !important; border-left: 1px solid #ddd; background: #eee; }
        
        /* 统计框样式 */
        .stat-box {
            text-align: center;
            padding: 20px;
        }
        .stat-box h4 {
            font-size: 2.5em;
            margin: 0;
            color: #2c3e50;
            font-weight: bold;
        }
        .stat-box p {
            margin: 10px 0 0 0;
            color: #7f8c8d;
            font-size: 14px;
        }
        
        /* 内容预览样式 */
        .content-preview {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        /* 操作按钮样式 */
        .action-buttons {
            white-space: nowrap;
        }
        .action-buttons .btn {
            margin-right: 5px;
            padding: 2px 8px;
            font-size: 11px;
        }
        
        /* 状态标签样式 */
        .status-label {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            color: white;
        }
        .status-published { background-color: #27ae60; }
        .status-draft { background-color: #f39c12; }
        .status-hidden { background-color: #e74c3c; }
        
        /* 模态框样式 */
        .modal-backdrop {
            position: fixed;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            z-index: 1040;
            background-color: #000;
            opacity: 0.5;
        }

        .modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 800px;
            max-width: 90vw;
            max-height: 90vh;
            z-index: 1050;
            background-color: #fff;
            border: 1px solid #999;
            border-radius: 6px;
            box-shadow: 0 3px 7px rgba(0,0,0,0.3);
            overflow-y: auto;
        }

        /* 确保模态框内容不会溢出 */
        .modal-body {
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .gritter-item-wrapper {
            z-index: 9999999;
        }

        /* 响应式模态框 */
        @media (max-width: 768px) {
            .modal {
                width: 95vw;
                max-width: 95vw;
                margin: 10px;
                top: 10px;
                left: 50%;
                transform: translateX(-50%);
                position: fixed;
            }

            .modal-body {
                max-height: 70vh;
            }
        }

        /* 修复Bootstrap模态框的一些问题 */
        .modal.fade.in {
            top: 50% !important;
            left: 50% !important;
            transform: translate(-50%, -50%) !important;
        }

        .modal.fade {
            transition: opacity 0.3s linear, top 0.3s ease-out, transform 0.3s ease-out;
        }
    </style>
</head>
<body>
<script src="js/head.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', () => {
        if (sessionStorage.getItem('adminLoggedIn') !== 'true') {
            window.location.href = '/login/login.html';
        }
    });
</script>
<div id="wrapper">
    <div id="content">
        <div id="content-header">
            <div id="breadcrumb">
                <a href="admin_home.html" title="返回首页" class="tip-bottom"><i class="icon-home"></i>首页</a>
                <a href="javascript:void(0);" class="current" onclick="return false;">技术支持内容管理</a>
            </div>
        </div>
        <div class="container-fluid">
            
            <!-- 统计信息 -->
            <div class="row-fluid">
                <div class="span3">
                    <div class="widget-box">
                        <div class="widget-content">
                            <div class="stat-box">
                                <h4 id="total-supports">0</h4>
                                <p>总详情内容数</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="span3">
                    <div class="widget-box">
                        <div class="widget-content">
                            <div class="stat-box">
                                <h4 id="total-menus">0</h4>
                                <p>已发布内容数</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="span3">
                    <div class="widget-box">
                        <div class="widget-content">
                            <div class="stat-box">
                                <h4 id="total-details">0</h4>
                                <p>草稿内容数</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="span3">
                    <div class="widget-box">
                        <div class="widget-content">
                            <div class="stat-box">
                                <h4 id="total-published">0</h4>
                                <p>最近更新数</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索和操作区域 -->
            <div class="widget-box">
                <div class="widget-content">
                    <div class="row-fluid">
                        <div class="span3">
                            <input type="text" id="search-title" placeholder="搜索标题..." class="input-medium">
                        </div>
                        <div class="span3">
                            <input type="text" id="search-content" placeholder="搜索内容..." class="input-medium">
                        </div>
                        <div class="span3">
                            <select id="status-filter" class="input-medium">
                                <option value="">所有状态</option>
                                <option value="1">已发布</option>
                                <option value="0">草稿</option>
                            </select>
                        </div>
                        <div class="span3">
                            <button class="btn btn-success" onclick="createNewSupport()">
                                <i class="icon-plus icon-white"></i> 新增内容
                            </button>
                            <button class="btn btn-primary" onclick="searchSupports()">搜索</button>
                            <button class="btn" onclick="resetSearch()">重置</button>
                            <a class="btn btn-danger" href="javascript:logout()" title="退出登录"><i class="icon icon-off"></i> 退出</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 支持内容列表 -->
            <div class="widget-box">
                <div class="widget-content nopadding">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>标题</th>
                                <th>内容预览</th>
                                <th>显示顺序</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="supports-list">
                            <!-- 动态加载内容 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 分页 -->
            <div class="fg-toolbar ui-toolbar ui-widget-header ui-corner-bl ui-corner-br ui-helper-clearfix">
                <div id="pagination" class="dataTables_paginate fg-buttonset ui-buttonset fg-buttonset-multi ui-buttonset-multi paging_full_numbers">
                    <!-- 动态生成分页 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 支持内容详情模态框 -->
<div id="supportDetailModal" class="modal hide fade">
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">×</button>
        <h3>支持内容详情</h3>
    </div>
    <div class="modal-body" id="support-detail-content">
        <!-- 动态加载支持内容详情 -->
    </div>
    <div class="modal-footer">
        <button class="btn btn-primary" onclick="editCurrentSupport()">编辑内容</button>
        <button class="btn btn-success" onclick="previewSupport()">预览页面</button>
        <button class="btn" data-dismiss="modal">关闭</button>
    </div>
</div>

<!-- 新增/编辑支持内容模态框 -->
<div id="editSupportModal" class="modal hide fade">
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">×</button>
        <h3 id="edit-modal-title">新增支持内容</h3>
    </div>
    <div class="modal-body">
        <form id="support-form">
            <div class="row-fluid">
                <div class="span6">
                    <label>类型</label>
                    <select id="support-type" class="span12" disabled>
                        <option value="详情">详情内容</option>
                    </select>
                    <small class="help-block">类型固定为详情内容</small>
                </div>
                <div class="span6">
                    <label>显示顺序</label>
                    <input type="number" id="support-order" class="span12" value="1">
                </div>
            </div>
            <div class="row-fluid">
                <div class="span12">
                    <label>标题</label>
                    <input type="text" id="support-title" class="span12" placeholder="请输入标题">
                </div>
            </div>
            <div class="row-fluid">
                <div class="span12">
                    <label>内容</label>
                    <textarea id="support-content" class="span12" rows="8" placeholder="请输入内容"></textarea>
                </div>
            </div>
            <div class="row-fluid">
                <div class="span6">
                    <label>链接URL</label>
                    <input type="text" id="support-url" class="span12" placeholder="可选">
                </div>
                <div class="span6">
                    <label>是否显示</label>
                    <select id="support-show" class="span12">
                        <option value="1">显示</option>
                        <option value="0">隐藏</option>
                    </select>
                </div>
            </div>
        </form>
    </div>
    <div class="modal-footer">
        <button class="btn btn-primary" onclick="saveSupport()">保存</button>
        <button class="btn" data-dismiss="modal">取消</button>
    </div>
</div>

<script src="js/bootstrap.min.js"></script>
<script src="js/jquery.gritter.min.js"></script>
<script src="js/support_content.js"></script>

<script>
// 修复模态框居中问题
$(document).ready(function() {
    // 当模态框显示时，确保它居中
    $('.modal').on('show', function() {
        var modal = $(this);
        var dialog = modal.find('.modal-dialog');

        // 重置位置
        modal.css({
            'position': 'fixed',
            'top': '50%',
            'left': '50%',
            'transform': 'translate(-50%, -50%)',
            'margin': '0'
        });
    });

    // 当模态框完全显示后，再次确保居中
    $('.modal').on('shown', function() {
        var modal = $(this);
        modal.css({
            'position': 'fixed',
            'top': '50%',
            'left': '50%',
            'transform': 'translate(-50%, -50%)',
            'margin': '0'
        });
    });

    // 窗口大小改变时重新居中
    $(window).on('resize', function() {
        $('.modal:visible').each(function() {
            var modal = $(this);
            modal.css({
                'position': 'fixed',
                'top': '50%',
                'left': '50%',
                'transform': 'translate(-50%, -50%)',
                'margin': '0'
            });
        });
    });
});
</script>
</body>
</html>
