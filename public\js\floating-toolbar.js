// 工具栏初始化函数
function initFloatingToolbar() {
    // 检测是否在子目录中
    const isInSubdir = window.location.pathname.split('/').length > 2;
    const pathPrefix = isInSubdir ? '../' : '';

    // 创建工具栏SVG对象
    var toolbar = document.createElement('object');
    toolbar.type = "image/svg+xml";
    toolbar.data = `${pathPrefix}images/home/<USER>
    toolbar.id = "floatingToolbar";
    toolbar.className = "floating-toolbar";
    toolbar.width = "119";
    toolbar.height = "371";
    toolbar.innerHTML = "您的浏览器不支持SVG";
    document.body.appendChild(toolbar);

    // 创建二维码弹窗
    var qrcodePopup = document.createElement('div');
    qrcodePopup.className = 'qrcode-popup';
    qrcodePopup.innerHTML = `
        <div class="qrcode-container">
            <div class="qrcode-item">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='104' height='104' viewBox='0 0 104 104'%3E%3Crect width='104' height='104' fill='%23fff'/%3E%3C/svg%3E" alt="企业微信" class="qrcode-image">
            </div>
            <div class="qrcode-item">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='104' height='104' viewBox='0 0 104 104'%3E%3Crect width='104' height='104' fill='%23fff'/%3E%3C/svg%3E" alt="个人微信" class="qrcode-image">
            </div>
        </div>
    `;
    document.body.appendChild(qrcodePopup);

    // 创建电话咨询弹窗
    var phonePopup = document.createElement('div');
    phonePopup.className = 'phone-popup';
    phonePopup.innerHTML = `
        <div class="phone-title">微信客服</div>
        <div class="qrcode-wrapper">
            <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='93' height='93' viewBox='0 0 93 93'%3E%3Crect width='93' height='93' fill='%23f0f0f0'/%3E%3C/svg%3E" alt="微信客服" class="qrcode-image">
        </div>
        <div class="work-time">
            <p>工作时间</p>
            <p>周一至周六 9:00-18:00</p>
            <p>服务热线: 4001-511-533</p>
        </div>
    `;
    document.body.appendChild(phonePopup);

    // 获取轮播图元素
    var carousel = document.querySelector('.carousel-bg');
    
    // 如果存在轮播图，调整工具栏位置
    if (carousel) {
        var carouselHeight = carousel.offsetHeight;
        var windowHeight = window.innerHeight;
        
        // 如果轮播图高度超过视窗高度的70%，将工具栏向下移动
        if (carouselHeight > windowHeight * 0.7) {
            toolbar.style.top = (carouselHeight + 50) + 'px';
            toolbar.style.transform = 'none';
        }
    }

    // 监听SVG加载完成事件
    toolbar.addEventListener('load', function() {
        console.log('工具栏SVG加载完成');
        
        // 获取SVG文档
        var svgDoc = toolbar.contentDocument;
        if (svgDoc) {
            // 为SVG内的元素添加点击事件
            var elements = svgDoc.querySelectorAll('[data-action]');
            elements.forEach(function(element) {
                element.addEventListener('click', function() {
                    var action = element.getAttribute('data-action');
                    handleToolbarAction(action);
                });
            });
        }
    });

    // 处理工具栏动作
    function handleToolbarAction(action) {
        switch(action) {
            case 'qrcode':
                toggleQrcodePopup();
                break;
            case 'phone':
                togglePhonePopup();
                break;
            case 'top':
                scrollToTop();
                break;
            default:
                console.log('未知动作:', action);
        }
    }

    // 切换二维码弹窗
    function toggleQrcodePopup() {
        qrcodePopup.style.display = qrcodePopup.style.display === 'block' ? 'none' : 'block';
        phonePopup.style.display = 'none'; // 关闭其他弹窗
    }

    // 切换电话咨询弹窗
    function togglePhonePopup() {
        phonePopup.style.display = phonePopup.style.display === 'block' ? 'none' : 'block';
        qrcodePopup.style.display = 'none'; // 关闭其他弹窗
    }

    // 滚动到顶部
    function scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    // 点击页面其他地方关闭弹窗
    document.addEventListener('click', function(e) {
        if (!toolbar.contains(e.target) && 
            !qrcodePopup.contains(e.target) && 
            !phonePopup.contains(e.target)) {
            qrcodePopup.style.display = 'none';
            phonePopup.style.display = 'none';
        }
    });

    // 监听窗口大小变化，调整工具栏位置
    window.addEventListener('resize', function() {
        if (carousel) {
            var carouselHeight = carousel.offsetHeight;
            var windowHeight = window.innerHeight;
            
            if (carouselHeight > windowHeight * 0.7) {
                toolbar.style.top = (carouselHeight + 50) + 'px';
                toolbar.style.transform = 'none';
            } else {
                toolbar.style.top = '50%';
                toolbar.style.transform = 'translateY(-50%)';
            }
        }
    });
}

// 页面加载完成后初始化工具栏
document.addEventListener('DOMContentLoaded', function() {
    // 延迟初始化，确保其他脚本已加载
    setTimeout(initFloatingToolbar, 1000);
});

// 如果jQuery可用，也可以使用jQuery的ready事件
if (typeof $ !== 'undefined') {
    $(document).ready(function() {
        setTimeout(initFloatingToolbar, 1000);
    });
}
