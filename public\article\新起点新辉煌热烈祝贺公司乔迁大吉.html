<!DOCTYPE html>
<html lang="zh-CN" style="height:100%">
<head>
    <meta charset="utf-8">
    <title>新起点、新辉煌——热烈祝贺公司乔迁大吉！ - 厦门贝启科技有限公司</title>
    <meta name="keywords" content="新起点、新辉煌——热烈祝贺公司乔迁大吉！">
    <link rel="icon" href="../images/home/<USER>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="description" content="新起点、新辉煌——热烈祝贺公司乔迁大吉！"/>
    <meta name="author" content=""/>
    <!-- css -->
    <link rel="stylesheet" href="../simple-line-icons/css/simple-line-icons.css">
    <link href="../css/bootstrap.min.css" rel="stylesheet"/>
    <link href="../css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="../css/flexslider.css" rel="stylesheet"/>
    <link href="../css/magnific-popup.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet"/>
    <link href="/css/nav-style.css" rel="stylesheet"/>
    <link href="../css/toolbar-custom.css" rel="stylesheet"/>
    <!-- Font Awesome 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?0558c2b79797ac39f3317053c376aaa2";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
    <style>
        .article-content {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .article-content img {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
        }
        .article-content h1, .article-content h2, .article-content h3 {
            margin: 20px 0 10px 0;
        }
        .article-content p {
            margin: 10px 0;
        }
    </style>
</head>
<body style="height:100%;overflow-x:hidden;" class="chinese-version">
<div id="wrapper" style="height:100%">

    <!-- start header -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header><!-- end header -->

    <section id="content" class="article_detail_cls">
        <div>
            <div class="row" style="margin-bottom: 0px;margin-top: -5em;">
            <hr/>
                <div>
                    <section id="article_info" style="display: block">
                        <div id="article_detail" class="article-content">
                            <h1>新起点、新辉煌——热烈祝贺公司乔迁大吉！</h1>
                            <p><span style="font-size: 18pt; color: #000000;">一、乔迁盛典</span></p><p><span style="font-size: 14pt; color: #000000;">2023年8月26日上午十点整，厦门贝启迎来八周年发展历程中的又一个重要里程碑&mdash;&mdash;正式入驻星网锐捷海西科技园，新址位于-福建省福州市高新区星网锐捷海西科技园1号楼东101单元。乔迁同时也预示着公司又一个全新的开端。 贝启科技刘总、陈总、罗总以及星网锐捷、瑞芯微、润和软件、德明通讯、福建省开源数字技术研究院等领导共同参加了此次活动。</span></p><p><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatre9XWQARHuZUnvPzzhttHDu3QuM6fNPWbYLYrrwxOia2J6wdLcrR7jA%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPat1CcSngZrb4rKZLDE5YemmlDyib8KoadO2ZxbpHaTV9VuQ9aT5HQZibuA%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatA3Ikk8hCoySGegHfuELibMs9l2TGd8jlCia4SEbFAVeIndnOtPGEYXDA%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /></p><p><span style="font-size: 14pt; color: #000000;">公司领导与莅临的领导及嘉宾们在大家的掌声和礼炮声中揭下红幕，标志着厦门贝启科技有限公司新办公室即日起正式启用</span></p><p><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatkuTjibIwiakCnermj9jGYNwn6Q1JJNxTKtRFpdNCIDFzaa2xtA7YiaBfw%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatwjzTvPnIUicasvQ3HqkanicjK5XxdORfc4LDxJ2sPK4OZrW53aXaj9aw%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /></p><p><span style="font-size: 18pt; color: #000000;">二、乔迁盛典现场图片</span></p><p><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPat87BN0KbQ1wmSADZic5n5HZd9PN57NmQnXnVFMBnIdPq86yfqWth5X6A%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatde696o8gubxoedN561Fiad3c3LqFYcibx2iaMY0moM3vP03icKSAlHBUaw%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatciaOHQJZURAyEMEVhxmicZrsssVXTiaGvEOMv4uwIC1CHdqMbR9SrCyuQ%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatBOSkkuUtKJTsvpXB8Lauo3d4UUWQA6kk1KsAkVeYibSTJF1Jd5aZpEA%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatic5rqAaAMJcSRs6EiaNWKW4GTibp1r75hTqFcxE9C466hPvALLb8iax5QQ%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatWVdGammhYSAbLUzTADOa25VLQbT9rcKDMnEibJA3ufnOibpjbnhmOzmw%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /></p><p><span style="font-size: 18pt; color: #000000;">三、高新科技企业核心聚集地</span></p><p><span style="font-size: 14pt; color: #000000;">星网锐捷海西科技园位于福建省福州市高新区，是中国500强聚集地，周边三公里云集众多高新科技上下游企业。便捷的上下游区位，将助力贝启科技继续腾跃至更高的企业战略目标。</span></p><p><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatp5R8V8gEZk8KDLuhp9HHDhWJqbZiagWE4icH8xibC0QiaD5tOl2piby20fQ%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /></p><p><span style="font-size: 18pt; color: #000000;">四、坚持深耕行业8年</span></p><p><span style="font-size: 14pt; color: #000000;">厦门贝启科技有限公司成立于2015年8月26日，是一家专注于开源软硬件设计的高科技企业；拥有授权知识产权38项、企业认定2项、技术认定1项、荣誉称号3项,并于2022年成为福建省开源数字技术研究院理事单位成员 。同时是96Boards硬件开源社区MP领导者合作伙伴，与（瑞芯微）、Linaro开源软件组织、ARM中国、Toybrick社区合作紧密。</span></p><p><span style="font-size: 14pt; color: #000000;">作为国内领先的嵌入式产品平台提供商，贝启科技始终致力于打造高品质核心板，已成功帮助超过500+家工业客户完成产品的快速开发与上市。</span></p><p><span style="font-size: 14pt; color: #000000;">近年来，公司紧密跟进国产化软硬件产品趋势，作为OpenHarmony项目群生态委员会教育专委会成员、医疗健康专委会成员，拥有全国产化硬件产品，我们积极适配OpenHarmony等国产化操作系统。拥有的RK3588/RK3568/RK3566行业主板/开发板均已适配支持OpenHarmony系统，已量产出货。</span></p><p><span style="font-size: 18pt; color: #000000;">五、核心产品展示</span></p><p><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_jpg%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatATicsMIuhAZ4xQ3eJrpdU4WbFzBbWLmdCCMMmxmkibqice6Q9NagdyPyw%2F640%3Fwx_fmt%3Djpeg" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_png%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatKdwb1j2oOYLJ8S64F80LdTCsq3bQLyNO7ibicGVuJ6z48vTXKiaGW0S3A%2F640%3Fwx_fmt%3Dpng" style="width: 100%; height: auto; margin: 10px 0;" /><img src="/apis/wechat_image_proxy/?url=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_jpg%2FfnCCcsb1IY8Zq7iavaHOxfU61icGewjPatV1JdFY7eQ2QibrW1bVfBDnb2J7nWSsscPhaXApickLAV0QIQuwnKYuvg%2F640%3Fwx_fmt%3Djpeg" style="width: 100%; height: auto; margin: 10px 0;" /></p><p><span style="font-size: 14pt; color: #000000;">此次乔迁，寓意着公司未来美好的发展与壮大。2023年，厦门贝启科技有限公司也将翻开崭新的一页，踏上新的征程！</span></p>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </section>

</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="../js/jquery.js"></script>
<script src="../js/jquery.easing.1.3.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script src="../js/jquery.fancybox.pack.js"></script>
<script src="../js/jquery.fancybox-media.js"></script>
<script src="../js/portfolio/jquery.quicksand.js"></script>
<script src="../js/portfolio/setting.js"></script>
<script src="../js/jquery.flexslider.js"></script>
<script src="../js/jquery.isotope.min.js"></script>
<script src="../js/jquery.magnific-popup.min.js"></script>
<script src="../js/animate.js"></script>
<script src="../js/custom.js"></script>
<script src="../js/tools.js"></script>
<script src="../js/nav.js"></script>
<script>window.FOOTER_MANUAL_INIT = true;</script>
<script src="../js/footer.js"></script>
<script src="../js/floating-toolbar.js"></script>
<script src="../js/toolbar-data.js"></script>
<script>
// 获取当前文章标题
var articleTitle = "新起点、新辉煌——热烈祝贺公司乔迁大吉！";

$(document).ready(function() {
    loadFooter();
});

function chg_lang() {
    const currentPath = window.location.pathname;
    if (currentPath.includes('_en.html')) {
        // 当前是英文页面，切换到中文
        window.location.href = currentPath.replace('_en.html', '.html');
    } else {
        // 当前是中文页面，切换到英文
        window.location.href = currentPath.replace('.html', '_en.html');
    }
}

function loadFooter() {
    console.log('🚀 开始加载页脚...');

    // 找到文章内容区域
    var insertTarget = $('#article_detail');
    console.log('📍 将在文章内容区域后插入页脚');

    // 如果页脚已存在，先移除
    if ($('footer').length > 0) {
        console.log('🗑️ 移除现有页脚');
        $('footer').remove();
    }

    // 在正确位置插入页脚
    insertTarget.after('<footer></footer>');
    console.log('📌 页脚已插入到正确位置');

    var showFooter = function() {
        if (typeof window.initFooter === 'function') {
            console.log('📝 开始初始化页脚内容...');
            window.initFooter();
            console.log('✨ 页脚内容加载完成');
        }
    };

    if (!window.initFooter) {
        console.log('📦 开始加载页脚脚本...');
        var script = document.createElement('script');
        script.src = '../js/footer.js';
        script.onload = showFooter;
        document.body.appendChild(script);
    } else {
        showFooter();
    }
}
</script>
</body>
</html>