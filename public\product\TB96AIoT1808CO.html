<!DOCTYPE html>
<html lang="zh-CN" style="height:100%">
<head>
    <meta charset="utf-8">
    <title>厦门贝启科技有限公司-Bearkey-官网</title>
    <meta name="keywords" content="TB-96AIoT-1808CO">
    <link rel="icon" href="../images/home/<USER>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="description" content="TB-96AIoT-1808CO"/>
    <meta name="author" content=""/>
    <!-- css -->
    <link rel="stylesheet" href="../simple-line-icons/css/simple-line-icons.css">
    <link href="../css/bootstrap.min.css" rel="stylesheet"/>
    <link href="../css/fancybox/jquery.fancybox.css" rel="stylesheet">
    <link href="../css/flexslider.css" rel="stylesheet"/>
    <link href="../css/magnific-popup.css" rel="stylesheet">
    <link href="../css/style.css" rel="stylesheet"/>
    <link href="/css/nav-style.css" rel="stylesheet"/>
    <!-- Font Awesome 图标库 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">

    <!-- HTML5 shim, for IE6-8 support of HTML5 elements -->
    <!--[if lt IE 9]>
    <script src="http://html5shim.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
    <script>
        var _hmt = _hmt || [];
        (function () {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?0558c2b79797ac39f3317053c376aaa2";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
</head>
<body style="height:100%;overflow-x:hidden;" class="chinese-version">
<div id="wrapper" style="height:100%">

    <!-- start header -->
    <header>
        <!-- 导航栏将通过nav.js动态加载 -->
    </header><!-- end header -->
    <section id="content" class="product_detail_cls" >
        <div>
            <div class="row" style="margin-bottom: 0px;margin-top: -5em;">
            <hr/>
                <div >
            
                
                    <section id="product_info" style="display: block">
                        <div id="product_detail">
                        <!-- 产品详情内容将通过AJAX动态加载 -->
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </section>

</div>
<a href="#" class="scrollup"><i class="fa fa-angle-up active"></i></a>

<!-- javascript
    ================================================== -->
<!-- Placed at the end of the document so the pages load faster -->
<script src="../js/jquery.js"></script>
<script src="../js/jquery.easing.1.3.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script src="../js/jquery.fancybox.pack.js"></script>
<script src="../js/jquery.fancybox-media.js"></script>
<script src="../js/portfolio/jquery.quicksand.js"></script>
<script src="../js/portfolio/setting.js"></script>
<script src="../js/jquery.flexslider.js"></script>
<script src="../js/jquery.isotope.min.js"></script>
<script src="../js/jquery.magnific-popup.min.js"></script>
<script src="../js/animate.js"></script>
<script src="../js/custom.js"></script>
<script src="../js/tools.js"></script>
<script src="../js/nav.js"></script>
<script>window.FOOTER_MANUAL_INIT = true;</script>
<script src="../js/footer.js"></script>
<script src="../js/floating-toolbar.js"></script>
<script src="../js/toolbar-data.js"></script>
<script>
// 获取当前产品类型
var productType = "TB-96AIoT-1808CO";

$(document).ready(function() {
    loadProductDetail();
});

function chg_lang() {
    window.location.href = "TB96AIoT1808CO_en.html";
}

function loadProductDetail() {
    $.ajax({
        type: "get",
        url: "/apis/product_detail_list/",
        data: { 
            filters: JSON.stringify({
                info_type: productType,
                lang: 0,
                show: 1
            })
        },
        success: function(response) {
            if (response.status === 'ok' && response.data && response.data.length > 0) {
                var productImages = response.data.sort(function(a, b) {
                    return a.display_order - b.display_order;
                });

                console.log('📊 从数据库获取到 ' + response.data.length + ' 条产品详情数据');

                var detailHtml = '';
                var imageCount = 0;
                productImages.forEach(function(item) {
                    if (item.image_path && item.show === 1) {
                        detailHtml += '<p><img src="' + item.image_path + '" alt="' + productType + '" width="100%" /></p>';
                        imageCount++;
                    }
                });

                console.log('🖼️ 需要加载 ' + imageCount + ' 张图片');
                $('#product_detail').html(detailHtml);

                // 等待图片加载完成后再加载页脚
                waitForImagesAndLoadFooter();
            } else {
                console.log('⚠️ 未找到产品详情数据，直接加载页脚');
                $('#product_detail').html('<p>暂无产品详情</p>');
                loadFooter();
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ 加载产品详情失败:', error);
            $('#product_detail').html('<p>加载产品详情失败</p>');
            console.log('⚠️ 加载失败，直接加载页脚');
            loadFooter();
        }
    });
}

function waitForImagesAndLoadFooter() {
    var images = $('#product_detail img');
    var totalImages = images.length;
    var loadedImages = 0;

    console.log('🔄 开始等待 ' + totalImages + ' 张图片加载完成...');

    if (totalImages === 0) {
        console.log('✅ 没有图片需要加载，直接加载页脚');
        loadFooter();
        return;
    }

    images.each(function(index) {
        var img = $(this)[0];

        // 如果图片已经加载完成
        if (img.complete && img.naturalHeight !== 0) {
            loadedImages++;
            console.log('✅ 图片 ' + (index + 1) + '/' + totalImages + ' 已加载完成 (缓存)');
            checkAllImagesLoaded();
        } else {
            // 监听图片加载事件
            $(this).on('load', function() {
                loadedImages++;
                console.log('✅ 图片 ' + (index + 1) + '/' + totalImages + ' 加载完成');
                checkAllImagesLoaded();
            }).on('error', function() {
                loadedImages++;
                console.log('❌ 图片 ' + (index + 1) + '/' + totalImages + ' 加载失败');
                checkAllImagesLoaded();
            });
        }
    });

    function checkAllImagesLoaded() {
        console.log('📈 图片加载进度: ' + loadedImages + '/' + totalImages);
        if (loadedImages === totalImages) {
            console.log('🎉 所有图片加载完成，开始加载页脚');
            loadFooter();
        }
    }
}

function loadFooter() {
    console.log('🔄 开始在最后一张图片后插入页脚...');

    // 找到所有产品详情图片
    var allImages = $('#product_detail img');
    console.log('🖼️ 找到图片数量:', allImages.length);

    // 找到最后一张图片
    var lastImage = allImages.last();
    var insertTarget;

    if (lastImage.length > 0) {
        // 找到最后一张图片的最外层容器
        var imageContainer = lastImage.closest('.col-md-6, .col-sm-6, .col-xs-12, .product-item, div');
        if (imageContainer.length > 0) {
            insertTarget = imageContainer;
            console.log('📍 将在最后一张图片容器后插入页脚');
        } else {
            insertTarget = lastImage;
            console.log('📍 将在最后一张图片元素后插入页脚');
        }
    } else {
        // 如果没有图片，插入到产品详情区域末尾
        insertTarget = $('#product_detail');
        console.log('📍 没有找到图片，将在产品详情区域末尾插入页脚');
    }

    // 如果页脚已存在，先移除
    if ($('footer').length > 0) {
        console.log('🗑️ 移除现有页脚');
        $('footer').remove();
    }

    // 在正确位置插入页脚
    insertTarget.after('<footer></footer>');
    console.log('📌 页脚已插入到正确位置');

    var showFooter = function() {
        if (typeof window.initFooter === 'function') {
            console.log('📝 开始初始化页脚内容...');
            window.initFooter();
            console.log('✨ 页脚内容加载完成');
        }
    };

    if (!window.initFooter) {
        console.log('📦 开始加载页脚脚本...');
        var script = document.createElement('script');
        script.src = '../js/footer.js';
        script.onload = showFooter;
        document.body.appendChild(script);
    } else {
        showFooter();
    }
}
</script>
</body>
</html>